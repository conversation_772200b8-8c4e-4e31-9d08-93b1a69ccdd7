from app import app, db
from models.user import User
from sqlalchemy import Column, DateTime
from datetime import datetime

# Create the context for the application
with app.app_context():
    # Add the new columns to the user table
    with db.engine.connect() as conn:
        conn.execute(db.text("ALTER TABLE user ADD COLUMN created_at DATETIME"))
        conn.execute(db.text("ALTER TABLE user ADD COLUMN updated_at DATETIME"))
        
        # Update existing records to have a default value
        conn.execute(db.text("UPDATE user SET created_at = ?, updated_at = ?"), 
                    (datetime.utcnow(), datetime.utcnow()))
        
        conn.commit()
    
    print("User table schema updated successfully!")