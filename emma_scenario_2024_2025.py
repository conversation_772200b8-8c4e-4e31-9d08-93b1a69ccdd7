#!/usr/bin/env python3
"""
Scenario: <PERSON> made full payments in 2024 but no payments in 2025
Calculate what she would owe in this scenario
"""

from flask import Flask
from models.database import db
from models.user import User
from models.property import Property
from models.payment import Payment
from models.client_property import ClientProperty
from models.client_service import ClientService
from models.service import Service
from models.support_ticket import SupportTicket
from models.ticket_message import TicketMessage
from datetime import date

def create_app():
    """Create Flask app with database configuration"""
    app = Flask(__name__)
    app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///greenpeak.db'
    app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
    db.init_app(app)
    return app

def calculate_scenario_debt():
    """Calculate Emma's debt in the scenario where she paid in 2024 but not 2025"""
    app = create_app()
    
    with app.app_context():
        print("=== SCENARIO: <PERSON> paid in 2024, no payments in 2025 ===\n")
        
        # Find Emma
        emma = User.query.filter_by(email='<EMAIL>').first()
        if not emma:
            print("Emma not found in database!")
            return
        
        print(f"Client: {emma.full_name} ({emma.email})")
        
        # Find Emma's properties
        client_properties = ClientProperty.query.filter_by(client_id=emma.id).all()
        
        if not client_properties:
            print("Emma has no assigned properties.")
            return
        
        total_debt = 0
        today = date.today()  # 2025-06-22
        
        print("--- SCENARIO ANALYSIS ---")
        print("Assumption: Emma was assigned properties in 2024 and made FULL payments for 2024")
        print("Assumption: Emma has made NO payments in 2025")
        print(f"Current date: {today}\n")
        
        for cp in client_properties:
            property_obj = Property.query.filter_by(id=cp.property_id).first()
            if property_obj:
                print(f"Property: {property_obj.name}")
                print(f"Annual Price: ${property_obj.price:,.2f}")
                print(f"Payment Cadence: {property_obj.payment_cadence}")
                
                # SCENARIO: Let's assume she was assigned in 2024 instead of 2025
                # and made full payment for 2024
                scenario_assignment_year = 2024
                scenario_assignment_date = date(2024, 1, 1)  # Assume assigned Jan 1, 2024
                
                print(f"SCENARIO - Assigned: {scenario_assignment_date}")
                print(f"SCENARIO - Paid for 2024: ${property_obj.price:,.2f} ✅")
                print(f"SCENARIO - Paid for 2025: $0.00 ❌")
                
                if property_obj.payment_cadence == 'annual':
                    # Calculate years from assignment to current date
                    years_since_assignment = today.year - scenario_assignment_year
                    
                    # Total expected payments from assignment year to current year
                    total_years_to_pay = years_since_assignment + 1  # +1 because we include the assignment year
                    total_expected = property_obj.price * total_years_to_pay
                    
                    # In this scenario, she paid for 2024 but not 2025
                    total_paid_in_scenario = property_obj.price  # Only 2024 payment
                    
                    amount_due = total_expected - total_paid_in_scenario
                    
                    print(f"Years since assignment: {years_since_assignment}")
                    print(f"Total years to pay for (2024-2025): {total_years_to_pay}")
                    print(f"Total expected: ${total_expected:,.2f}")
                    print(f"Total paid (2024 only): ${total_paid_in_scenario:,.2f}")
                    print(f"Amount due for 2025: ${amount_due:,.2f}")
                    
                    # Break down what she owes
                    if amount_due > 0:
                        print(f"BREAKDOWN:")
                        print(f"  - 2024 payment: ${property_obj.price:,.2f} ✅ PAID")
                        print(f"  - 2025 payment: ${property_obj.price:,.2f} ❌ UNPAID")
                        print(f"  - Total owed: ${amount_due:,.2f}")
                    
                    total_debt += amount_due
                
                print("-" * 50)
        
        print(f"\n" + "="*60)
        print(f"=== SCENARIO RESULT ===")
        print(f"If Emma paid FULL amounts in 2024 but NOTHING in 2025:")
        print(f"Emma would owe: ${total_debt:,.2f}")
        print(f"="*60)
        
        if total_debt == 0:
            print("🎉 Emma would be up to date!")
        else:
            print("⚠️  Emma would owe money for the current year (2025).")
            print(f"This represents her annual payment obligation for 2025.")
        
        # Compare with current reality
        print(f"\n--- COMPARISON ---")
        print(f"SCENARIO (paid 2024, nothing in 2025): ${total_debt:,.2f}")
        print(f"REALITY (partial payments in 2025): $2,730,000.00")
        print(f"Difference: ${abs(total_debt - 2730000):,.2f}")

if __name__ == "__main__":
    calculate_scenario_debt()
