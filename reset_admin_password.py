from app import app
from models.database import db
from models.user import User
from werkzeug.security import generate_password_hash

def reset_admin_password():
    with app.app_context():
        # Find the admin user
        admin = User.query.filter_by(email='<EMAIL>').first()
        
        if not admin:
            print("Admin user not found. Creating new admin user...")
            admin = User(
                email='<EMAIL>',
                firstname='Admin',
                lastname='User',
                role='super_user',
                active=True
            )
            db.session.add(admin)
        
        # Reset password
        new_password = 'Gr33nP3@k5hel+3rs2025'
        admin.password_hash = generate_password_hash(new_password)
        
        # Save changes
        db.session.commit()
        
        print(f"Admin password reset successfully. Email: {admin.email}, Password: {new_password}")
        
        # Verify password
        verification = admin.check_password(new_password)
        print(f"Password verification test: {verification}")

if __name__ == "__main__":
    reset_admin_password()