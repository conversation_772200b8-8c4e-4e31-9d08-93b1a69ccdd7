
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Green Peak Shelters - Staff Dashboard</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <!-- Chosen Palette: Light Gray & White Base with Green Accents. Background: #F9FAFB (light gray). Card Backgrounds: #FFFFFF (white). Accent Greens: #96C84C (Lemon Green), #224F34 (Forest Green). Card Accents: Orange, Blue, Green, Indigo, Red, Purple for visual distinction. Text: Dark Gray (#374151). -->
    <!-- Application Structure Plan: A dashboard layout with a fixed left sidebar for navigation and a main content area. The main content area is divided into an overview section (metrics, date), a dynamic project cards grid, a financial analytics chart, and a right-aligned client messages section. This structure provides quick access to key performance indicators, a visual summary of projects, financial trends, and direct client communication, enabling staff to efficiently manage their daily tasks and gain insights within a single interactive page. -->
    <!-- Visualization & Content Choices:
        1. Overview Metrics (In Progress, Upcoming, Total Projects): -> Goal: Provide quick, high-level status. -> Viz/Presentation Method: Large text figures with descriptive labels. -> Interaction: Clickable to filter project cards (simulated). -> Justification: Immediate understanding of workload. -> Library/Method: HTML/CSS.
        2. Project Cards: -> Goal: Show individual project status and key details. -> Viz/Presentation Method: Customizable card components with progress bars. -> Interaction: Hover for subtle effect; click to open a modal for more details (simulated). -> Justification: Visually engaging, provides digestible chunks of information. -> Library/Method: HTML/CSS.
        3. Monthly Payments Chart: -> Goal: Visualize year-to-date financial trends. -> Viz/Presentation Method: Bar Chart using Chart.js on a Canvas element. -> Interaction: Hover for tooltips. -> Justification: Clear representation of payment flow over time. -> Library/Method: Chart.js (Canvas).
        4. Client Messages: -> Goal: Display recent client communications. -> Viz/Presentation Method: Scrollable list of message snippets with avatars. -> Interaction: Scroll. -> Justification: Keeps staff informed of active client queries. -> Library/Method: HTML/CSS.
        Confirmation: NO SVG graphics used. NO Mermaid JS used. -->
    <style>
        body {
            font-family: 'Inter', sans-serif;
            background-color: #F9FAFB; /* Light background */
            color: #374151; /* Dark text for contrast */
        }
        .chart-container {
            position: relative;
            width: 100%;
            max-width: 800px; /* Max width for chart to prevent overstretching */
            margin-left: auto;
            margin-right: auto;
            height: 300px; /* Base height for small screens */
            max-height: 400px; /* Max height to constrain */
        }
        @media (min-width: 768px) { /* Medium screens */
            .chart-container {
                height: 350px;
            }
        }
        @media (min-width: 1024px) { /* Large screens */
            .chart-container {
                height: 400px;
            }
        }
        .text-xxs {
            font-size: 0.65rem;
            line-height: 0.8rem;
        }
        /* Custom scrollbar for message section */
        .custom-scrollbar::-webkit-scrollbar {
            width: 8px;
        }
        .custom-scrollbar::-webkit-scrollbar-track {
            background: #E5E7EB; /* Lighter track */
            border-radius: 10px;
        }
        .custom-scrollbar::-webkit-scrollbar-thumb {
            background: #9CA3AF; /* Lighter thumb */
            border-radius: 10px;
        }
        .custom-scrollbar::-webkit-scrollbar-thumb:hover {
            background: #6B7280;
        }
        /* Adjusted for dark sidebar */
        .sidebar-icon-active {
            background-color: rgba(150, 200, 76, 0.2); /* Lemon Green with transparency */
            color: #96C84C; /* Lemon Green for active icon text */
        }
        /* Override default Chart.js tooltip background */
        .chartjs-tooltip {
            background-color: rgba(0, 0, 0, 0.7) !important; /* Keep tooltips dark for readability */
            color: white !important;
        }
    </style>
</head>
<body class="flex flex-col lg:flex-row min-h-screen">

    <!-- Top Header Bar for Mobile/Tablet -->
    <header class="lg:hidden flex items-center justify-between p-4 bg-white shadow-md z-10">
        <button id="mobile-menu-toggle" class="text-gray-700 text-2xl p-2 rounded-md hover:bg-gray-200">☰</button>
        <div class="text-xl font-semibold text-gray-800">Green Peak Shelters</div>
        <div class="flex items-center space-x-4">
            <span class="text-gray-700 text-xl">🔍</span>
            <span class="text-gray-700 text-xl">🔔</span>
            <div class="w-8 h-8 rounded-full bg-gray-300 flex items-center justify-center text-gray-700 text-sm">JS</div>
        </div>
    </header>

    <!-- Sidebar Navigation -->
    <nav id="sidebar" class="fixed inset-y-0 left-0 w-20 bg-[#224F34] text-gray-200 p-4 flex flex-col items-center shadow-lg transform -translate-x-full lg:translate-x-0 transition-transform duration-300 ease-in-out z-20">
        <div class="mb-8">
             <img src="https://www.greenpeakshelter.com/assets/img/logo.png" alt="GP Logo" class="h-10 w-auto" onerror="this.onerror=null; this.src='https://placehold.co/40x40/224F34/FFFFFF?text=GP';">
        </div>
        <ul class="space-y-6 w-full">
            <li>
                <a href="#" class="flex flex-col items-center p-2 rounded-xl hover:bg-green-700 group sidebar-icon-active">
                    <span class="text-2xl">🏠</span>
                    <span class="text-xs mt-1">Home</span>
                </a>
            </li>
            <li>
                <a href="#" class="flex flex-col items-center p-2 rounded-xl hover:bg-green-700 group">
                    <span class="text-2xl">📊</span>
                    <span class="text-xs mt-1">Analytics</span>
                </a>
            </li>
            <li>
                <a href="#" class="flex flex-col items-center p-2 rounded-xl hover:bg-green-700 group">
                    <span class="text-2xl">📋</span>
                    <span class="text-xs mt-1">Projects</span>
                </a>
            </li>
            <li>
                <a href="#" class="flex flex-col items-center p-2 rounded-xl hover:bg-green-700 group">
                    <span class="text-2xl">👥</span>
                    <span class="text-xs mt-1">Clients</span>
                </a>
            </li>
            <li>
                <a href="#" class="flex flex-col items-center p-2 rounded-xl hover:bg-green-700 group">
                    <span class="text-2xl">💰</span>
                    <span class="text-xs mt-1">Payments</span>
                </a>
            </li>
            <li>
                <a href="#" class="flex flex-col items-center p-2 rounded-xl hover:bg-green-700 group">
                    <span class="text-2xl">⚙️</span>
                    <span class="text-xs mt-1">Settings</span>
                </a>
            </li>
        </ul>
    </nav>

    <!-- Main Content Area -->
    <main class="flex-1 overflow-y-auto p-4 lg:ml-20 transition-all duration-300 ease-in-out">
        <!-- Main Content Header -->
        <div class="flex flex-col sm:flex-row sm:items-center justify-between mb-6">
            <div class="flex items-center space-x-4">
                <h1 class="text-3xl font-bold text-gray-800">Home</h1>
                <span class="hidden lg:inline-block text-xl text-gray-700">🔍</span>
                <input type="text" placeholder="Search..." class="hidden lg:inline-block bg-gray-100 text-gray-700 px-4 py-2 rounded-xl focus:outline-none focus:ring-1 focus:ring-green-500">
            </div>
            <div class="mt-4 sm:mt-0 flex items-center space-x-4 text-sm">
                <span class="text-gray-500">December, 12</span>
                <div class="hidden lg:flex items-center space-x-2">
                    <span class="text-gray-700 text-xl">🔗</span>
                    <span class="text-gray-700 text-xl">🔔</span>
                    <div class="w-8 h-8 rounded-full bg-gray-300 flex items-center justify-center text-gray-700 text-sm">JS</div>
                </div>
            </div>
        </div>

        <!-- Overview Metrics Section -->
        <section class="mb-8">
            <h2 class="text-lg font-semibold text-gray-700 mb-4">Project Overview</h2>
            <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
                <div class="bg-white p-6 rounded-xl shadow-md text-center">
                    <p class="text-4xl font-bold text-gray-800 mb-2">45</p>
                    <p class="text-gray-500">In Progress</p>
                </div>
                <div class="bg-white p-6 rounded-xl shadow-md text-center">
                    <p class="text-4xl font-bold text-gray-800 mb-2">24</p>
                    <p class="text-gray-500">Upcoming</p>
                </div>
                <div class="bg-white p-6 rounded-xl shadow-md text-center">
                    <p class="text-4xl font-bold text-gray-800 mb-2">62</p>
                    <p class="text-gray-500">Total Projects</p>
                </div>
            </div>
        </section>

        <!-- Dynamic Content Area -->
        <div class="flex flex-col lg:flex-row gap-6">
            <div class="flex-1">
                <!-- Projects Grid Section -->
                <section class="mb-8">
                    <div class="flex justify-between items-center mb-4">
                        <h2 class="text-lg font-semibold text-gray-700">My Projects</h2>
                        <div class="flex space-x-2">
                            <button class="bg-white p-2 rounded-lg text-gray-600 hover:bg-gray-100">☰</button>
                            <button class="bg-[#224F34] p-2 rounded-lg text-white hover:bg-green-800">▦</button>
                        </div>
                    </div>
                    <div id="projects-grid" class="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-4">
                        <!-- Project cards will be injected here by JavaScript -->
                    </div>
                </section>

                <!-- Monthly Payments Chart Section -->
                <section class="mb-8 bg-white p-6 rounded-xl shadow-md">
                    <h2 class="text-lg font-semibold text-gray-700 mb-4">Year-to-Date Payments (₦)</h2>
                    <p class="text-sm text-gray-500 mb-4">This chart visualizes the total amounts paid for year to date, showing monthly payment trends in Naira (₦).</p>
                    <div class="chart-container">
                        <canvas id="paymentsChart"></canvas>
                    </div>
                     <div class="mt-4 flex flex-col sm:flex-row justify-around text-center text-sm">
                        <div class="flex items-center justify-center my-1 sm:my-0">
                            <span class="w-3 h-3 rounded-full bg-red-500 mr-2"></span>
                            <span class="text-gray-500">Overdue Payments: <span class="font-bold text-red-600" id="overdue-count">5</span></span>
                        </div>
                        <div class="flex items-center justify-center my-1 sm:my-0">
                            <span class="w-3 h-3 rounded-full bg-blue-500 mr-2"></span>
                            <span class="text-gray-500">Payments Due: <span class="font-bold text-blue-600" id="due-count">8</span></span>
                        </div>
                        <div class="flex items-center justify-center my-1 sm:my-0">
                            <span class="w-3 h-3 rounded-full bg-green-500 mr-2"></span>
                            <span class="text-gray-500">Recent Payments: <span class="font-bold text-green-600" id="recent-count">12</span></span>
                        </div>
                    </div>
                </section>
            </div>

            <!-- Client Messages Section -->
            <aside class="w-full lg:w-80 bg-white p-6 rounded-xl shadow-md flex-shrink-0">
                <h2 class="text-lg font-semibold text-gray-700 mb-4">Client Messages</h2>
                <div id="messages-list" class="space-y-4 max-h-[calc(100vh-20rem)] overflow-y-auto custom-scrollbar">
                    <!-- Messages will be injected here by JavaScript -->
                </div>
            </aside>
        </div>
    </main>

    <!-- Modal for Project Details (Hidden by default) -->
    <div id="project-modal" class="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50 hidden p-4">
        <div class="bg-white p-8 rounded-xl shadow-2xl w-full max-w-lg relative">
            <button id="close-modal-btn" class="absolute top-4 right-4 text-gray-400 hover:text-gray-700 text-3xl">&times;</button>
            <h3 id="modal-title" class="text-2xl font-bold text-gray-800 mb-4"></h3>
            <p id="modal-date" class="text-sm text-gray-500 mb-2"></p>
            <p id="modal-subtask" class="text-md text-gray-700 mb-4"></p>
            <div class="w-full bg-gray-300 rounded-full h-2.5 mb-4">
                <div id="modal-progress-bar" class="h-2.5 rounded-full" style="width: 0%;"></div>
            </div>
            <p id="modal-progress-text" class="text-sm text-gray-500 mb-4"></p>
            <p id="modal-days-left" class="text-sm text-gray-500"></p>
            <p class="text-sm text-gray-600 mt-6">This is a placeholder for more detailed project information.</p>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', () => {
            const projectsData = [
                { id: 1, date: 'December 10, 2020', title: 'Web Designing', subtask: 'Prototyping', progress: 60, daysLeft: 2, color: 'bg-orange-500', members: ['👤', '👩‍💻', '👨‍💻'] },
                { id: 2, date: 'December 10, 2020', title: 'Mobile App Dev', subtask: 'UI/UX Design', progress: 75, daysLeft: 5, color: 'bg-blue-500', members: ['🧑‍💻', '👱‍♀️'] },
                { id: 3, date: 'December 10, 2020', title: 'CRM Integration', subtask: 'Backend Dev', progress: 90, daysLeft: 1, color: 'bg-green-500', members: ['🧔', '👩‍💻'] },
                { id: 4, date: 'December 10, 2020', title: 'Content Strategy', subtask: 'Drafting', progress: 40, daysLeft: 10, color: 'bg-indigo-500', members: ['✍️', '💡'] },
                { id: 5, date: 'December 10, 2020', title: 'SEO Optimization', subtask: 'Keyword Research', progress: 80, daysLeft: 3, color: 'bg-red-500', members: ['📈', '📊'] },
                { id: 6, date: 'December 10, 2020', title: 'E-commerce Redesign', subtask: 'Frontend Dev', progress: 65, daysLeft: 7, color: 'bg-purple-500', members: ['🛒', '🧑‍💻'] },
                { id: 7, date: 'December 15, 2020', title: 'Marketing Campaign', subtask: 'Ad Creation', progress: 30, daysLeft: 15, color: 'bg-lime-500', members: ['📣', '🎨'] },
                { id: 8, date: 'December 18, 2020', title: 'Database Migration', subtask: 'Data Mapping', progress: 50, daysLeft: 12, color: 'bg-teal-500', members: ['💾', '⚙️'] },
            ];

            const messagesData = [
                { name: 'Stephanie', message: 'I got your first assignment. It was quite good. We can continue with the next assignment.', date: 'Dec, 12', avatar: 'https://placehold.co/40x40/FF7F50/FFFFFF?text=S' },
                { name: 'Mark', message: 'Hey, can tell me about progress of project? I\'m waiting for your response.', date: 'Dec, 12', avatar: 'https://placehold.co/40x40/6495ED/FFFFFF?text=M' },
                { name: 'David', message: 'Hey, can tell me about progress of project? I\'m waiting for your response.', date: 'Dec, 12', avatar: 'https://placehold.co/40x40/DAA520/FFFFFF?text=D' },
                { name: 'Mark', message: 'I am really impressed! Can\'t wait to see the final result.', date: 'Dec, 12', avatar: 'https://placehold.co/40x40/6495ED/FFFFFF?text=M' },
                { name: 'Alice', message: 'Could you please send over the latest report by end of day?', date: 'Dec, 11', avatar: 'https://placehold.co/40x40/8A2BE2/FFFFFF?text=A' },
                { name: 'John', message: 'I have reviewed the designs and they look fantastic!', date: 'Dec, 11', avatar: 'https://placehold.co/40x40/A9A9A9/FFFFFF?text=J' },
                { name: 'Sarah', message: 'Meeting scheduled for tomorrow at 10 AM regarding project Alpha.', date: 'Dec, 10', avatar: 'https://placehold.co/40x40/FFD700/FFFFFF?text=S' },
            ];

            // Mock financial data for the chart (Naira ₦)
            const paymentsData = {
                labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'],
                datasets: [{
                    label: 'Monthly Payments (₦)',
                    data: [1500000, 1800000, 1200000, 2000000, 1600000, 2500000, 1900000, 2100000, 1700000, 2300000, 2600000, 2800000],
                    backgroundColor: '#96C84C', // Lemon Green
                    borderColor: '#224F34', // Forest Green
                    borderWidth: 1,
                    borderRadius: 4,
                }]
            };

            // Project Grid Rendering
            const projectsGrid = document.getElementById('projects-grid');
            const renderProjects = (filter = 'all') => {
                projectsGrid.innerHTML = '';
                const filteredProjects = projectsData.filter(project => {
                    if (filter === 'all') return true;
                    if (filter === 'in_progress') return project.progress < 100;
                    if (filter === 'upcoming') return project.daysLeft > 5 && project.progress < 10; // Mock condition for upcoming
                    return true;
                });

                filteredProjects.forEach(project => {
                    const projectCard = document.createElement('div');
                    projectCard.classList.add('bg-white', 'p-4', 'rounded-xl', 'shadow-md', 'flex', 'flex-col', 'justify-between', 'hover:scale-105', 'transition-transform', 'duration-200', 'cursor-pointer');
                    projectCard.innerHTML = `
                        <p class="text-xxs text-gray-500 mb-2">${project.date}</p>
                        <h3 class="text-md font-semibold text-gray-800 mb-1">${project.title}</h3>
                        <p class="text-xs text-gray-500 mb-4">${project.subtask}</p>
                        <div class="w-full bg-gray-300 rounded-full h-2.5 mb-2">
                            <div class="h-2.5 rounded-full ${project.color}" style="width: ${project.progress}%;"></div>
                        </div>
                        <div class="flex justify-between items-center text-xs text-gray-500 mb-4">
                            <span>Progress</span>
                            <span>${project.progress}%</span>
                        </div>
                        <div class="flex items-center justify-between">
                            <div class="flex -space-x-1 overflow-hidden">
                                ${project.members.map(member => `<span class="inline-block h-6 w-6 rounded-full ring-2 ring-white bg-gray-400 flex items-center justify-center text-sm">${member}</span>`).join('')}
                            </div>
                            <span class="text-xxs text-red-600 bg-red-100 px-2 py-1 rounded-full">${project.daysLeft} Days Left</span>
                        </div>
                    `;
                    projectCard.addEventListener('click', () => openProjectModal(project));
                    projectsGrid.appendChild(projectCard);
                });
            };

            renderProjects();

            // Client Messages Rendering
            const messagesList = document.getElementById('messages-list');
            messagesData.forEach(message => {
                const messageDiv = document.createElement('div');
                messageDiv.classList.add('flex', 'items-start', 'space-x-3', 'p-2', 'rounded-lg', 'hover:bg-gray-100', 'cursor-pointer');
                messageDiv.innerHTML = `
                    <img src="${message.avatar}" alt="${message.name} avatar" class="w-10 h-10 rounded-full flex-shrink-0">
                    <div class="flex-1">
                        <div class="flex justify-between items-center">
                            <p class="font-medium text-gray-800">${message.name}</p>
                            <p class="text-xxs text-gray-500">${message.date}</p>
                        </div>
                        <p class="text-sm text-gray-500">${message.message}</p>
                    </div>
                `;
                messagesList.appendChild(messageDiv);
            });

            // Payments Chart
            const ctx = document.getElementById('paymentsChart').getContext('2d');
            const paymentsChart = new Chart(ctx, {
                type: 'bar',
                data: paymentsData,
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    let label = context.dataset.label || '';
                                    if (label) {
                                        label += ': ';
                                    }
                                    if (context.parsed.y !== null) {
                                        label += '₦' + context.parsed.y.toLocaleString();
                                    }
                                    return label;
                                },
                                title: function(tooltipItems) {
                                    return 'Month: ' + tooltipItems[0].label;
                                },
                                beforeBody: function(tooltipItems) {
                                    return 'Total Amount Paid';
                                }
                            }
                        },
                        legend: {
                            display: false,
                        }
                    },
                    scales: {
                        x: {
                            grid: {
                                color: 'rgba(0, 0, 0, 0.1)', /* Lighter grid lines */
                            },
                            ticks: {
                                color: '#374151', /* Darker ticks */
                            }
                        },
                        y: {
                            beginAtZero: true,
                            grid: {
                                color: 'rgba(0, 0, 0, 0.1)', /* Lighter grid lines */
                            },
                            ticks: {
                                color: '#374151', /* Darker ticks */
                                callback: function(value) {
                                    return '₦' + (value / 1000000).toFixed(1) + 'M';
                                }
                            }
                        }
                    }
                }
            });

            // Dynamic Counts for Payments (Mock Data)
            document.getElementById('overdue-count').textContent = '5';
            document.getElementById('due-count').textContent = '8';
            document.getElementById('recent-count').textContent = '12';

            // Mobile menu toggle
            const mobileMenuToggle = document.getElementById('mobile-menu-toggle');
            const sidebar = document.getElementById('sidebar');
            mobileMenuToggle.addEventListener('click', () => {
                sidebar.classList.toggle('-translate-x-full');
            });

            // Project Modal Logic
            const projectModal = document.getElementById('project-modal');
            const closeModalBtn = document.getElementById('close-modal-btn');
            const modalTitle = document.getElementById('modal-title');
            const modalDate = document.getElementById('modal-date');
            const modalSubtask = document.getElementById('modal-subtask');
            const modalProgressBar = document.getElementById('modal-progress-bar');
            const modalProgressText = document.getElementById('modal-progress-text');
            const modalDaysLeft = document.getElementById('modal-days-left');

            const openProjectModal = (project) => {
                modalTitle.textContent = project.title;
                modalDate.textContent = project.date;
                modalSubtask.textContent = project.subtask;
                modalProgressBar.style.width = `${project.progress}%`;
                modalProgressBar.className = `h-2.5 rounded-full ${project.color}`;
                modalProgressText.textContent = `Progress: ${project.progress}%`;
                modalDaysLeft.textContent = `Days Left: ${project.daysLeft}`;
                projectModal.classList.remove('hidden');
            };

            closeModalBtn.addEventListener('click', () => {
                projectModal.classList.add('hidden');
            });

            projectModal.addEventListener('click', (e) => {
                if (e.target === projectModal) {
                    projectModal.classList.add('hidden');
                }
            });
        });
    </script>
</body>
</html>
