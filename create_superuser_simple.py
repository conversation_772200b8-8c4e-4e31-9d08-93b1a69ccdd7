#!/usr/bin/env python3
"""
Simple script to create superuser account directly in database
"""

import sqlite3
import bcrypt
import os
from datetime import datetime

def create_superuser():
    """Create superuser account directly in SQLite database"""
    
    # Database path
    db_path = 'instance/greenpeak.db'
    
    if not os.path.exists(db_path):
        print(f"❌ Database not found at {db_path}")
        return False
    
    print("=== Creating Superuser Account ===\n")
    
    # Credentials from README
    email = "<EMAIL>"
    password = "Gr33nP3@k5hel+3rs2025"
    
    try:
        # Connect to database
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Check if user table exists
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='user'")
        if not cursor.fetchone():
            print("❌ User table does not exist. Please run the Flask app first to create tables.")
            conn.close()
            return False
        
        # Check existing users
        print("Checking existing users...")
        cursor.execute('SELECT id, email, firstname, lastname, role, active FROM user')
        users = cursor.fetchall()
        
        print(f"Found {len(users)} existing users:")
        for user in users:
            print(f"  - ID: {user[0]}, Email: {user[1]}, Name: {user[2]} {user[3]}, Role: {user[4]}, Active: {user[5]}")
        
        # Check if superuser already exists
        cursor.execute('SELECT * FROM user WHERE email = ?', (email,))
        existing = cursor.fetchone()
        
        if existing:
            print(f"\n✅ Superuser already exists with email: {email}")
            conn.close()
            return True
        
        print(f"\n📝 Creating superuser account...")
        print(f"Email: {email}")
        print(f"Password: {password}")
        
        # Create bcrypt hash for password
        salt = bcrypt.gensalt(rounds=12)
        password_hash = bcrypt.hashpw(password.encode('utf-8'), salt).decode('utf-8')
        
        # Get current timestamp
        now = datetime.utcnow().isoformat()
        
        # Insert superuser
        cursor.execute('''
            INSERT INTO user (
                email, password_hash, firstname, lastname, role, active, 
                designation, department, created_at, updated_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            email,
            password_hash,
            'System',
            'Administrator', 
            'super_user',
            1,  # True for active
            'System Administrator',
            'Administration',
            now,
            now
        ))
        
        # Commit changes
        conn.commit()
        
        # Verify creation
        cursor.execute('SELECT id, email, firstname, lastname, role FROM user WHERE email = ?', (email,))
        new_user = cursor.fetchone()
        
        if new_user:
            print(f"\n✅ Superuser created successfully!")
            print(f"   ID: {new_user[0]}")
            print(f"   Email: {new_user[1]}")
            print(f"   Name: {new_user[2]} {new_user[3]}")
            print(f"   Role: {new_user[4]}")
            print(f"   Password: Encrypted with bcrypt (12 rounds)")
            
            print(f"\n🎉 You can now log in with:")
            print(f"   URL: http://127.0.0.1:5000/login")
            print(f"   Email: {email}")
            print(f"   Password: {password}")
            
            conn.close()
            return True
        else:
            print(f"❌ Failed to create superuser")
            conn.close()
            return False
            
    except Exception as e:
        print(f"❌ Error: {e}")
        if 'conn' in locals():
            conn.close()
        return False

if __name__ == '__main__':
    success = create_superuser()
    if not success:
        print(f"\n❌ Failed to create superuser account.")
        exit(1)
