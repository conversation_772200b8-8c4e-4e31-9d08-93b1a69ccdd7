/* Custom CSS Variables */
:root {
    --primary-50: #f0f9ff;
    --primary-100: #e0f2fe;
    --primary-200: #bae6fd;
    --primary-300: #7dd3fc;
    --primary-400: #38bdf8;
    --primary-500: #0ea5e9;
    --primary-600: #0284c7;
    --primary-700: #0369a1;
    --primary-800: #075985;
    --primary-900: #0c4a6e;
    
    --secondary-50: #f0fdf4;
    --secondary-100: #dcfce7;
    --secondary-200: #bbf7d0;
    --secondary-300: #86efac;
    --secondary-400: #4ade80;
    --secondary-500: #22c55e;
    --secondary-600: #16a34a;
    --secondary-700: #15803d;
    --secondary-800: #166534;
    --secondary-900: #14532d;
}

/* Tailwind Extensions */
.bg-primary-50 { background-color: var(--primary-50); }
.bg-primary-100 { background-color: var(--primary-100); }
.bg-primary-200 { background-color: var(--primary-200); }
.bg-primary-300 { background-color: var(--primary-300); }
.bg-primary-400 { background-color: var(--primary-400); }
.bg-primary-500 { background-color: var(--primary-500); }
.bg-primary-600 { background-color: var(--primary-600); }
.bg-primary-700 { background-color: var(--primary-700); }
.bg-primary-800 { background-color: var(--primary-800); }
.bg-primary-900 { background-color: var(--primary-900); }

.text-primary-50 { color: var(--primary-50); }
.text-primary-100 { color: var(--primary-100); }
.text-primary-200 { color: var(--primary-200); }
.text-primary-300 { color: var(--primary-300); }
.text-primary-400 { color: var(--primary-400); }
.text-primary-500 { color: var(--primary-500); }
.text-primary-600 { color: var(--primary-600); }
.text-primary-700 { color: var(--primary-700); }
.text-primary-800 { color: var(--primary-800); }
.text-primary-900 { color: var(--primary-900); }

.bg-secondary-50 { background-color: var(--secondary-50); }
.bg-secondary-100 { background-color: var(--secondary-100); }
.bg-secondary-200 { background-color: var(--secondary-200); }
.bg-secondary-300 { background-color: var(--secondary-300); }
.bg-secondary-400 { background-color: var(--secondary-400); }
.bg-secondary-500 { background-color: var(--secondary-500); }
.bg-secondary-600 { background-color: var(--secondary-600); }
.bg-secondary-700 { background-color: var(--secondary-700); }
.bg-secondary-800 { background-color: var(--secondary-800); }
.bg-secondary-900 { background-color: var(--secondary-900); }

.text-secondary-50 { color: var(--secondary-50); }
.text-secondary-100 { color: var(--secondary-100); }
.text-secondary-200 { color: var(--secondary-200); }
.text-secondary-300 { color: var(--secondary-300); }
.text-secondary-400 { color: var(--secondary-400); }
.text-secondary-500 { color: var(--secondary-500); }
.text-secondary-600 { color: var(--secondary-600); }
.text-secondary-700 { color: var(--secondary-700); }
.text-secondary-800 { color: var(--secondary-800); }
.text-secondary-900 { color: var(--secondary-900); }

/* Custom Components */
.btn {
    @apply px-4 py-2 rounded-md font-medium focus:outline-none focus:ring-2 focus:ring-offset-2 transition-colors;
}

.btn-primary {
    @apply bg-primary-600 text-white hover:bg-primary-700 focus:ring-primary-500;
}

.btn-secondary {
    @apply bg-secondary-600 text-white hover:bg-secondary-700 focus:ring-secondary-500;
}

.btn-outline {
    @apply border border-gray-300 text-gray-700 bg-white hover:bg-gray-50 focus:ring-primary-500;
}

.btn-danger {
    @apply bg-red-600 text-white hover:bg-red-700 focus:ring-red-500;
}

.btn-sm {
    @apply px-3 py-1 text-sm;
}

.btn-lg {
    @apply px-6 py-3 text-lg;
}

.form-input {
    @apply mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500;
}

.form-label {
    @apply block text-sm font-medium text-gray-700 mb-1;
}

.form-group {
    @apply mb-4;
}

.card {
    @apply bg-white rounded-lg shadow-md overflow-hidden;
}

.card-header {
    @apply px-6 py-4 bg-gray-50 border-b border-gray-200;
}

.card-body {
    @apply p-6;
}

.card-footer {
    @apply px-6 py-4 bg-gray-50 border-t border-gray-200;
}

.badge {
    @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
}

.badge-success {
    @apply bg-green-100 text-green-800;
}

.badge-warning {
    @apply bg-yellow-100 text-yellow-800;
}

.badge-danger {
    @apply bg-red-100 text-red-800;
}

.badge-info {
    @apply bg-blue-100 text-blue-800;
}

/* Sidebar active link styles */
.sidebar-link-active {
    @apply bg-gray-900 text-white;
}

.sidebar-link-inactive {
    @apply text-gray-300 hover:bg-gray-700 hover:text-white;
}

/* Responsive adjustments for sidebar layout */
@media (max-width: 768px) {
    .sidebar {
        display: none;
    }
}