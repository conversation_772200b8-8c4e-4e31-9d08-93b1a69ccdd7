from flask import Flask, render_template, g, redirect, url_for, request, session
from routes.auth import auth_bp
from routes.admin import admin_bp
from routes.staff import staff_bp
from routes.client import client_bp
from models.database import db
from models.user import User
from models.property import Property
from models.service import Service
from models.payment import Payment
from models.payment_submission import PaymentSubmission
from models.notification import Notification
from models.session import Session
from utils.auth import load_user
from utils.session_manager import DatabaseSessionInterface
import os
import secrets
import datetime
import locale

app = Flask(__name__, static_folder='assets', static_url_path='/assets', template_folder='views')
app.config['SECRET_KEY'] = secrets.token_hex(16)  # Generate a secure random key

# Configure database session interface
app.session_interface = DatabaseSessionInterface()

# Add context processor for current year in templates
@app.context_processor
def inject_now():
    return {'now': datetime.datetime.now()}

# Add context processor for current user
@app.context_processor
def inject_user():
    return {'user': g.get('user', None)}

# Custom Jinja2 filters
@app.template_filter('format_currency')
def format_currency(value):
    """Format a number as currency with commas for thousands"""
    try:
        return "{:,.2f}".format(float(value))
    except (ValueError, TypeError):
        return "0.00"

# Session configuration
app.config.update(
    SESSION_COOKIE_SECURE=True,
    SESSION_COOKIE_HTTPONLY=True,
    SESSION_COOKIE_SAMESITE='Lax',
    PERMANENT_SESSION_LIFETIME=3600  # 1 hour
)

# Database configuration
app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///greenpeak.db'
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
db.init_app(app)

# Register blueprints
app.register_blueprint(auth_bp)
app.register_blueprint(admin_bp)
app.register_blueprint(staff_bp)
app.register_blueprint(client_bp)

# Before request handler to load user
@app.before_request
def load_user_before_request():
    # Don't use the return value of load_user
    from utils.auth import load_user
    load_user()

# Debug request handler
@app.before_request
def debug_request():
    print(f"Request path: {request.path}")
    print(f"g.user: {g.get('user')}")
    print(f"session: {dict(session)}")
    print(f"session_id: {getattr(session, 'session_id', 'None')}")

# Periodic cleanup of expired sessions
@app.before_request
def cleanup_sessions():
    """Periodically clean up expired sessions"""
    import random
    # Run cleanup on ~1% of requests to avoid performance impact
    if random.random() < 0.01:
        try:
            from utils.auth import cleanup_expired_sessions
            expired_count = cleanup_expired_sessions()
            if expired_count > 0:
                app.logger.info(f"Cleaned up {expired_count} expired sessions")
        except Exception as e:
            app.logger.error(f"Error during session cleanup: {e}")

# Error handlers
@app.errorhandler(404)
def page_not_found(e):
    return render_template('error/404.html'), 404

@app.errorhandler(500)
def server_error(e):
    return render_template('error/500.html'), 500

# Root route
@app.route('/')
def index():
    if g.user:
        if g.user.role == 'admin':
            return redirect(url_for('admin.dashboard'))
        elif g.user.role == 'staff':
            return redirect(url_for('staff.dashboard'))
        elif g.user.role == 'client':
            return redirect(url_for('client.dashboard'))
    return redirect(url_for('auth.login'))

if __name__ == '__main__':
    with app.app_context():
        db.create_all()

        # Clean up any existing expired sessions on startup
        try:
            from utils.auth import cleanup_expired_sessions
            expired_count = cleanup_expired_sessions()
            if expired_count > 0:
                print(f"Cleaned up {expired_count} expired sessions on startup")
        except Exception as e:
            print(f"Error during startup session cleanup: {e}")

    app.run(debug=True)
