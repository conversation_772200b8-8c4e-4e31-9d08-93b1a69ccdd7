#!/usr/bin/env python3
"""
Password Migration Utility

This script helps migrate existing Werkzeug password hashes to bcrypt.
Since we can't reverse hash passwords, this migration happens automatically
during user login when they provide their correct password.

This utility provides tools to:
1. Check which users need password migration
2. Force password reset for users with legacy hashes
3. Monitor migration progress
"""

from models.user import User
from models.database import db
from flask import current_app
import sys
from datetime import datetime

def check_migration_status():
    """Check how many users need password migration"""
    total_users = User.query.count()
    bcrypt_users = User.query.filter(User.password_hash.like('$2b$%')).count()
    legacy_users = total_users - bcrypt_users
    
    print(f"Password Migration Status:")
    print(f"  Total users: {total_users}")
    print(f"  Bcrypt users: {bcrypt_users}")
    print(f"  Legacy users: {legacy_users}")
    print(f"  Migration progress: {(bcrypt_users/total_users*100):.1f}%" if total_users > 0 else "  No users found")
    
    return {
        'total': total_users,
        'bcrypt': bcrypt_users,
        'legacy': legacy_users,
        'progress': (bcrypt_users/total_users*100) if total_users > 0 else 0
    }

def list_legacy_users():
    """List users with legacy password hashes"""
    legacy_users = User.query.filter(~User.password_hash.like('$2b$%')).all()
    
    if not legacy_users:
        print("No users with legacy password hashes found.")
        return []
    
    print(f"Users with legacy password hashes ({len(legacy_users)}):")
    for user in legacy_users:
        print(f"  - {user.email} ({user.role}) - Created: {user.created_at}")
    
    return legacy_users

def force_password_reset_for_legacy_users():
    """
    Mark all users with legacy hashes to require password reset
    This is useful if you want to force immediate migration
    """
    legacy_users = User.query.filter(~User.password_hash.like('$2b$%')).all()
    
    if not legacy_users:
        print("No users with legacy password hashes found.")
        return 0
    
    print(f"Marking {len(legacy_users)} users for password reset...")
    
    for user in legacy_users:
        # You could add a password_reset_required field to User model
        # For now, we'll just print what would be done
        print(f"  - Would mark {user.email} for password reset")
    
    # Uncomment if you add password_reset_required field:
    # try:
    #     for user in legacy_users:
    #         user.password_reset_required = True
    #     db.session.commit()
    #     print(f"Successfully marked {len(legacy_users)} users for password reset.")
    # except Exception as e:
    #     db.session.rollback()
    #     print(f"Error marking users for password reset: {e}")
    #     return 0
    
    return len(legacy_users)

def verify_bcrypt_hashes():
    """Verify that all bcrypt hashes are properly formatted"""
    bcrypt_users = User.query.filter(User.password_hash.like('$2b$%')).all()
    
    print(f"Verifying {len(bcrypt_users)} bcrypt hashes...")
    
    valid_count = 0
    invalid_count = 0
    
    for user in bcrypt_users:
        try:
            # Check if hash format is valid
            parts = user.password_hash.split('$')
            if len(parts) == 4 and parts[1] == '2b' and parts[2].isdigit():
                rounds = int(parts[2])
                if 4 <= rounds <= 31:  # Valid bcrypt rounds range
                    valid_count += 1
                else:
                    print(f"  - {user.email}: Invalid rounds ({rounds})")
                    invalid_count += 1
            else:
                print(f"  - {user.email}: Invalid hash format")
                invalid_count += 1
        except Exception as e:
            print(f"  - {user.email}: Error validating hash - {e}")
            invalid_count += 1
    
    print(f"Verification complete:")
    print(f"  Valid hashes: {valid_count}")
    print(f"  Invalid hashes: {invalid_count}")
    
    return valid_count, invalid_count

def get_password_security_report():
    """Generate a comprehensive password security report"""
    print("=== Password Security Report ===")
    print(f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # Migration status
    status = check_migration_status()
    print()
    
    # Legacy users
    legacy_users = list_legacy_users()
    print()
    
    # Bcrypt verification
    valid, invalid = verify_bcrypt_hashes()
    print()
    
    # Security recommendations
    print("Security Recommendations:")
    if status['legacy'] > 0:
        print(f"  - {status['legacy']} users still have legacy password hashes")
        print("  - These will be automatically upgraded when users log in")
        print("  - Consider forcing password reset for immediate migration")
    
    if invalid > 0:
        print(f"  - {invalid} users have invalid bcrypt hashes")
        print("  - These users should be forced to reset their passwords")
    
    if status['bcrypt'] > 0:
        print(f"  - {status['bcrypt']} users have secure bcrypt password hashes")
    
    print("  - Ensure password policy is enforced for new passwords")
    print("  - Monitor failed login attempts for security threats")
    print("  - Consider implementing account lockout after failed attempts")

def main():
    """Main function for command-line usage"""
    if len(sys.argv) < 2:
        print("Usage: python migrate_passwords.py <command>")
        print("Commands:")
        print("  status     - Check migration status")
        print("  list       - List users with legacy hashes")
        print("  verify     - Verify bcrypt hash integrity")
        print("  report     - Generate full security report")
        print("  reset      - Mark legacy users for password reset")
        return
    
    command = sys.argv[1].lower()
    
    if command == 'status':
        check_migration_status()
    elif command == 'list':
        list_legacy_users()
    elif command == 'verify':
        verify_bcrypt_hashes()
    elif command == 'report':
        get_password_security_report()
    elif command == 'reset':
        force_password_reset_for_legacy_users()
    else:
        print(f"Unknown command: {command}")

if __name__ == '__main__':
    # This would need to be run in Flask app context
    print("This script needs to be run within Flask application context.")
    print("Example usage in Flask shell:")
    print("  from utils.migrate_passwords import get_password_security_report")
    print("  get_password_security_report()")
