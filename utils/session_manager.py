from flask.sessions import SessionInterface, SessionMixin
from werkzeug.datastructures import CallbackDict
from flask import request, current_app
from models.session import Session
from models.database import db
from datetime import datetime, timedelta
import uuid

class DatabaseSession(CallbackDict, SessionMixin):
    """Custom session class that stores data in database"""
    
    def __init__(self, initial=None, session_id=None, new=False):
        def on_update(self):
            self.modified = True
        
        CallbackDict.__init__(self, initial, on_update)
        self.session_id = session_id
        self.new = new
        self.modified = False
        self.permanent = False
    
    def __repr__(self):
        return f'<DatabaseSession {self.session_id}>'

class DatabaseSessionInterface(SessionInterface):
    """Custom session interface that uses database for session storage"""
    
    session_class = DatabaseSession
    
    def __init__(self):
        self.session_timeout = 3600  # 1 hour default
    
    def generate_sid(self):
        """Generate a new session ID"""
        return Session.generate_session_id()
    
    def get_cookie_name(self, app):
        """Get the name of the session cookie"""
        return app.config.get('SESSION_COOKIE_NAME', 'session')
    
    def get_cookie_domain(self, app):
        """Get the domain for the session cookie"""
        return app.config.get('SESSION_COOKIE_DOMAIN')
    
    def get_cookie_path(self, app):
        """Get the path for the session cookie"""
        return app.config.get('SESSION_COOKIE_PATH', '/')
    
    def get_cookie_httponly(self, app):
        """Check if session cookie should be HTTP only"""
        return app.config.get('SESSION_COOKIE_HTTPONLY', True)
    
    def get_cookie_secure(self, app):
        """Check if session cookie should be secure"""
        return app.config.get('SESSION_COOKIE_SECURE', False)
    
    def get_cookie_samesite(self, app):
        """Get the SameSite attribute for session cookie"""
        return app.config.get('SESSION_COOKIE_SAMESITE', 'Lax')
    
    def get_expiration_time(self, app, session):
        """Get session expiration time"""
        if session.permanent:
            return datetime.utcnow() + app.permanent_session_lifetime
        return datetime.utcnow() + timedelta(seconds=self.session_timeout)
    
    def should_set_cookie(self, app, session):
        """Check if we should set the session cookie"""
        return session.modified or session.new
    
    def open_session(self, app, request):
        """Load session from database"""
        session_id = request.cookies.get(self.get_cookie_name(app))
        
        if not session_id:
            # No session cookie, create new session
            return self.session_class(session_id=None, new=True)
        
        # Try to load session from database
        db_session = Session.get_valid_session(session_id)
        
        if not db_session:
            # Invalid or expired session, create new one
            return self.session_class(session_id=None, new=True)
        
        # Load session data
        session_data = db_session.get_data()
        
        # Update session activity
        db_session.updated_at = datetime.utcnow()
        
        # Extend expiry if it's a permanent session
        if session_data.get('permanent', False):
            db_session.extend_expiry(app.permanent_session_lifetime.total_seconds())
        else:
            db_session.extend_expiry(self.session_timeout)
        
        try:
            db.session.commit()
        except Exception as e:
            current_app.logger.error(f"Error updating session: {e}")
            db.session.rollback()
        
        # Create Flask session object
        flask_session = self.session_class(
            initial=session_data,
            session_id=session_id,
            new=False
        )
        
        flask_session.permanent = session_data.get('permanent', False)
        return flask_session
    
    def save_session(self, app, session, response):
        """Save session to database"""
        domain = self.get_cookie_domain(app)
        path = self.get_cookie_path(app)
        
        if not session:
            # Empty session, remove cookie if it exists
            if session.modified:
                response.delete_cookie(
                    self.get_cookie_name(app),
                    domain=domain,
                    path=path
                )
            return
        
        # Don't save if session hasn't been modified and isn't new
        if not self.should_set_cookie(app, session):
            return
        
        # Get or create database session
        if session.new or not session.session_id:
            # Create new session
            session_id = self.generate_sid()
            
            # Calculate expiry time
            if session.permanent:
                expires_in = int(app.permanent_session_lifetime.total_seconds())
            else:
                expires_in = self.session_timeout
            
            # Get client info
            ip_address = self.get_client_ip(request)
            user_agent = request.headers.get('User-Agent', '')[:500]
            
            # Create database session
            try:
                db_session = Session.create_session(
                    user_id=session.get('user_id'),
                    ip_address=ip_address,
                    user_agent=user_agent,
                    expires_in=expires_in
                )
                session_id = db_session.id
                session.session_id = session_id
                
            except Exception as e:
                current_app.logger.error(f"Error creating session: {e}")
                return
        else:
            # Update existing session
            db_session = Session.query.filter_by(id=session.session_id).first()
            if not db_session:
                current_app.logger.warning(f"Session {session.session_id} not found in database")
                return
        
        # Update session data
        session_data = dict(session)
        session_data['permanent'] = session.permanent
        
        try:
            db_session.set_data(session_data)
            db_session.user_id = session.get('user_id')
            db.session.commit()
        except Exception as e:
            current_app.logger.error(f"Error saving session: {e}")
            db.session.rollback()
            return
        
        # Set cookie
        expires = self.get_expiration_time(app, session)
        response.set_cookie(
            self.get_cookie_name(app),
            session.session_id,
            expires=expires,
            httponly=self.get_cookie_httponly(app),
            domain=domain,
            path=path,
            secure=self.get_cookie_secure(app),
            samesite=self.get_cookie_samesite(app)
        )
    
    def get_client_ip(self, request):
        """Get client IP address, handling proxies"""
        # Check for forwarded headers (common in production with load balancers)
        if request.headers.get('X-Forwarded-For'):
            return request.headers.get('X-Forwarded-For').split(',')[0].strip()
        elif request.headers.get('X-Real-IP'):
            return request.headers.get('X-Real-IP')
        else:
            return request.remote_addr
    
    def cleanup_expired_sessions(self):
        """Clean up expired sessions (call this periodically)"""
        try:
            return Session.cleanup_expired_sessions()
        except Exception as e:
            current_app.logger.error(f"Error cleaning up sessions: {e}")
            return 0
