import re
from typing import Dict, List, Tuple

class PasswordPolicy:
    """Password policy enforcement and validation"""
    
    def __init__(self):
        # Default password policy settings
        self.min_length = 8
        self.max_length = 128
        self.require_uppercase = True
        self.require_lowercase = True
        self.require_digits = True
        self.require_special_chars = True
        self.min_special_chars = 1
        self.forbidden_patterns = [
            r'(.)\1{2,}',  # No more than 2 consecutive identical characters
            r'(012|123|234|345|456|567|678|789|890)',  # No sequential numbers
            r'(abc|bcd|cde|def|efg|fgh|ghi|hij|ijk|jkl|klm|lmn|mno|nop|opq|pqr|qrs|rst|stu|tuv|uvw|vwx|wxy|xyz)',  # No sequential letters
        ]
        self.common_passwords = {
            'password', 'password123', '123456', '123456789', 'qwerty', 'abc123',
            'password1', 'admin', 'letmein', 'welcome', 'monkey', '1234567890',
            'dragon', 'master', 'hello', 'freedom', 'whatever', 'qazwsx',
            'trustno1', 'jordan23', 'harley', 'robert', 'matthew', 'jordan',
            'michelle', 'daniel', 'andrew', 'joshua', 'hunter', 'football'
        }
    
    def validate_password(self, password: str, username: str = None) -> Tuple[bool, List[str]]:
        """
        Validate password against policy
        
        Returns:
            Tuple of (is_valid, list_of_errors)
        """
        errors = []
        
        if not password:
            errors.append("Password is required")
            return False, errors
        
        # Length checks
        if len(password) < self.min_length:
            errors.append(f"Password must be at least {self.min_length} characters long")
        
        if len(password) > self.max_length:
            errors.append(f"Password must not exceed {self.max_length} characters")
        
        # Character requirements
        if self.require_uppercase and not re.search(r'[A-Z]', password):
            errors.append("Password must contain at least one uppercase letter")
        
        if self.require_lowercase and not re.search(r'[a-z]', password):
            errors.append("Password must contain at least one lowercase letter")
        
        if self.require_digits and not re.search(r'\d', password):
            errors.append("Password must contain at least one digit")
        
        if self.require_special_chars:
            special_chars = re.findall(r'[!@#$%^&*()_+\-=\[\]{};\':"\\|,.<>\/?~`]', password)
            if len(special_chars) < self.min_special_chars:
                errors.append(f"Password must contain at least {self.min_special_chars} special character(s)")
        
        # Forbidden patterns
        for pattern in self.forbidden_patterns:
            if re.search(pattern, password.lower()):
                errors.append("Password contains forbidden patterns (sequential or repeated characters)")
                break
        
        # Common password check
        if password.lower() in self.common_passwords:
            errors.append("Password is too common, please choose a more unique password")
        
        # Username similarity check
        if username and len(username) >= 3:
            if username.lower() in password.lower() or password.lower() in username.lower():
                errors.append("Password must not contain your username")
        
        return len(errors) == 0, errors
    
    def get_password_strength(self, password: str) -> Dict:
        """
        Calculate password strength score and provide feedback
        
        Returns:
            Dictionary with strength score, level, and feedback
        """
        if not password:
            return {
                'score': 0,
                'level': 'Very Weak',
                'feedback': ['Password is required']
            }
        
        score = 0
        feedback = []
        
        # Length scoring
        if len(password) >= 8:
            score += 1
        if len(password) >= 12:
            score += 1
        if len(password) >= 16:
            score += 1
        
        # Character variety scoring
        if re.search(r'[a-z]', password):
            score += 1
        if re.search(r'[A-Z]', password):
            score += 1
        if re.search(r'\d', password):
            score += 1
        if re.search(r'[!@#$%^&*()_+\-=\[\]{};\':"\\|,.<>\/?~`]', password):
            score += 1
        
        # Complexity bonus
        char_types = sum([
            bool(re.search(r'[a-z]', password)),
            bool(re.search(r'[A-Z]', password)),
            bool(re.search(r'\d', password)),
            bool(re.search(r'[!@#$%^&*()_+\-=\[\]{};\':"\\|,.<>\/?~`]', password))
        ])
        
        if char_types >= 3:
            score += 1
        if char_types == 4:
            score += 1
        
        # Determine strength level
        if score <= 2:
            level = 'Very Weak'
            feedback.append('Use a longer password with mixed characters')
        elif score <= 4:
            level = 'Weak'
            feedback.append('Add more character variety and length')
        elif score <= 6:
            level = 'Fair'
            feedback.append('Consider adding more special characters')
        elif score <= 8:
            level = 'Good'
            feedback.append('Strong password!')
        else:
            level = 'Excellent'
            feedback.append('Excellent password strength!')
        
        return {
            'score': score,
            'level': level,
            'feedback': feedback
        }
    
    def generate_password_requirements_text(self) -> str:
        """Generate human-readable password requirements"""
        requirements = []
        
        requirements.append(f"At least {self.min_length} characters long")
        
        if self.require_uppercase:
            requirements.append("At least one uppercase letter")
        
        if self.require_lowercase:
            requirements.append("At least one lowercase letter")
        
        if self.require_digits:
            requirements.append("At least one number")
        
        if self.require_special_chars:
            requirements.append(f"At least {self.min_special_chars} special character(s)")
        
        requirements.append("Must not contain common passwords or your username")
        requirements.append("Must not contain sequential or repeated characters")
        
        return "Password must meet the following requirements:\n• " + "\n• ".join(requirements)

# Global password policy instance
password_policy = PasswordPolicy()

def validate_password(password: str, username: str = None) -> Tuple[bool, List[str]]:
    """Convenience function for password validation"""
    return password_policy.validate_password(password, username)

def get_password_strength(password: str) -> Dict:
    """Convenience function for password strength calculation"""
    return password_policy.get_password_strength(password)

def get_password_requirements() -> str:
    """Convenience function for password requirements text"""
    return password_policy.generate_password_requirements_text()
