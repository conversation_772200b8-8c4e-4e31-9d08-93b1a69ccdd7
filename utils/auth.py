
from functools import wraps
from flask import session, redirect, url_for, request, flash, g, current_app
from models.user import User
from models.session import Session
from models.database import db
from datetime import datetime

def login_required(f):
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if 'user_id' not in session:
            return redirect(url_for('auth.login', next=request.url))
        return f(*args, **kwargs)
    return decorated_function

def role_required(roles):
    def decorator(view):
        @wraps(view)
        def wrapped_view(**kwargs):
            if g.user is None:
                return redirect(url_for('auth.login'))

            if g.user.role not in roles:
                flash('You do not have permission to access this page.', 'error')
                return redirect(url_for('auth.login'))

            return view(**kwargs)
        return wrapped_view
    return decorator

def load_user():
    """Load user from session and validate session"""
    user_id = session.get('user_id')
    if user_id:
        g.user = User.query.get(user_id)

        # Validate session in database if using database sessions
        if hasattr(session, 'session_id') and session.session_id:
            db_session = Session.get_valid_session(session.session_id)
            if not db_session:
                # Session is invalid, clear it
                session.clear()
                g.user = None
                current_app.logger.warning(f"Invalid session {session.session_id} cleared")
    else:
        g.user = None

    # Store session info in g for debugging
    g.session_id = getattr(session, 'session_id', None)
    return None

def create_user_session(user, remember_me=False, ip_address=None, user_agent=None):
    """Create a new session for a user"""
    # Clear any existing session
    session.clear()

    # Set session data
    session['user_id'] = user.id
    session['user_role'] = user.role
    session['login_time'] = datetime.utcnow().isoformat()

    if remember_me:
        session.permanent = True

    # The session will be automatically saved to database by DatabaseSessionInterface
    return session

def invalidate_user_sessions(user_id, except_current=True):
    """Invalidate all sessions for a user"""
    current_session_id = getattr(session, 'session_id', None) if except_current else None
    return Session.invalidate_user_sessions(user_id, except_session_id=current_session_id)

def get_user_sessions(user_id, active_only=True):
    """Get all sessions for a user"""
    return Session.get_user_sessions(user_id, active_only=active_only)

def cleanup_expired_sessions():
    """Clean up expired sessions"""
    return Session.cleanup_expired_sessions()
