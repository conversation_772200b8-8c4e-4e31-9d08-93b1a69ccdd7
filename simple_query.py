#!/usr/bin/env python3
"""
Simple database query script for GreenPeak application
"""

from flask import Flask
from models.database import db
from models.user import User
from models.property import Property
from models.service import Service
from models.payment import Payment
from models.client_property import ClientProperty
from models.client_service import ClientService
from models.support_ticket import SupportTicket
from models.ticket_message import TicketMessage

def create_app():
    """Create Flask app with database configuration"""
    app = Flask(__name__)
    app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///greenpeak.db'
    app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
    db.init_app(app)
    return app

def run_simple_queries():
    """Run some simple database queries"""
    app = create_app()
    
    with app.app_context():
        print("=== GreenPeak Database Query Results ===\n")
        
        # Query 1: Count all users
        user_count = User.query.count()
        print(f"Total users in database: {user_count}")
        
        # Query 2: List all users with their roles
        print("\n--- All Users ---")
        users = User.query.all()
        for user in users:
            print(f"ID: {user.id}, Name: {user.full_name}, Email: {user.email}, Role: {user.role}, Active: {user.active}")
        
        # Query 3: Count users by role
        print("\n--- Users by Role ---")
        roles = db.session.query(User.role, db.func.count(User.id)).group_by(User.role).all()
        for role, count in roles:
            print(f"{role}: {count} users")
        
        # Query 4: Check if there are any properties
        property_count = Property.query.count()
        print(f"\nTotal properties in database: {property_count}")
        
        if property_count > 0:
            print("\n--- Properties ---")
            properties = Property.query.limit(5).all()  # Show first 5 properties
            for prop in properties:
                print(f"ID: {prop.id}, Name: {prop.name}, Location: {prop.location}, Type: {prop.property_type}, Price: ${prop.price}, Status: {prop.status}")
        
        # Query 5: Check if there are any services
        service_count = Service.query.count()
        print(f"\nTotal services in database: {service_count}")
        
        if service_count > 0:
            print("\n--- Services ---")
            services = Service.query.limit(5).all()  # Show first 5 services
            for service in services:
                print(f"ID: {service.id}, Name: {service.name}, Price: ${service.price}")
        
        # Query 6: Check if there are any payments
        payment_count = Payment.query.count()
        print(f"\nTotal payments in database: {payment_count}")
        
        if payment_count > 0:
            print("\n--- Recent Payments ---")
            payments = Payment.query.order_by(Payment.created_at.desc()).limit(5).all()
            for payment in payments:
                print(f"ID: {payment.id}, Amount: ${payment.amount}, Status: {payment.status}, Date: {payment.created_at}")

if __name__ == "__main__":
    run_simple_queries()
