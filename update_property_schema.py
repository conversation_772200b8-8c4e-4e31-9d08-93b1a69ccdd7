from app import app, db
from sqlalchemy import Column, String

# Create the context for the application
with app.app_context():
    # Add the new column to the property table
    with db.engine.connect() as conn:
        conn.execute(db.text("ALTER TABLE property ADD COLUMN payment_cadence VARCHAR(20) DEFAULT 'one_time'"))
        conn.commit()
    
    print("Property table schema updated successfully!")