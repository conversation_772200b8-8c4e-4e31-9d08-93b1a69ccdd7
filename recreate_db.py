import os
from app import app, db
from models.user import User
from werkzeug.security import generate_password_hash

def init_db():
    """Initialize the database with a super user"""
    # Create super user
    super_user = User(
        email='<EMAIL>',
        firstname='Admin',
        lastname='User',
        role='super_user',
        active=True,
        password_hash=generate_password_hash('Gr33nP3@k5hel+3rs2025')
    )
    
    # Add to database
    db.session.add(super_user)
    db.session.commit()
    
    print("Super user created successfully.")

# Create the context for the application
with app.app_context():
    # Get the database file path
    db_path = app.config['SQLALCHEMY_DATABASE_URI'].replace('sqlite:///', '')
    
    # Remove the database file if it exists
    if os.path.exists(db_path):
        os.remove(db_path)
        print(f"Removed existing database at {db_path}")
    
    # Create all tables
    db.create_all()
    print("Database tables created successfully!")
    
    # Initialize the database with the super user
    init_db()
    print("Database initialized with super user")
