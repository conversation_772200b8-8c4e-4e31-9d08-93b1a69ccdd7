#!/usr/bin/env python3
"""
Test script for database session management
"""

from app import app
from models.database import db
from models.session import Session
from models.user import User

def test_database_sessions():
    """Test database session functionality"""
    
    with app.app_context():
        print("=== Testing Database Session Management ===")
        
        # 1. Create tables
        print("1. Creating database tables...")
        try:
            db.create_all()
            print("   ✅ Tables created successfully")
        except Exception as e:
            print(f"   ❌ Error creating tables: {e}")
            return False
        
        # 2. Test session creation
        print("2. Testing session creation...")
        try:
            test_session = Session.create_session(
                user_id=1,
                ip_address='127.0.0.1',
                user_agent='Test Browser',
                expires_in=3600
            )
            print(f"   ✅ Session created with ID: {test_session.id}")
        except Exception as e:
            print(f"   ❌ Error creating session: {e}")
            return False
        
        # 3. Test session retrieval
        print("3. Testing session retrieval...")
        try:
            retrieved_session = Session.get_valid_session(test_session.id)
            if retrieved_session:
                print(f"   ✅ Session retrieved successfully")
            else:
                print(f"   ❌ Session not found")
                return False
        except Exception as e:
            print(f"   ❌ Error retrieving session: {e}")
            return False
        
        # 4. Test session data
        print("4. Testing session data...")
        try:
            test_data = {'user_id': 1, 'test_key': 'test_value'}
            retrieved_session.set_data(test_data)
            db.session.commit()
            
            # Retrieve again and check data
            retrieved_session = Session.get_valid_session(test_session.id)
            session_data = retrieved_session.get_data()
            
            if session_data.get('test_key') == 'test_value':
                print(f"   ✅ Session data stored and retrieved correctly")
            else:
                print(f"   ❌ Session data mismatch")
                return False
        except Exception as e:
            print(f"   ❌ Error with session data: {e}")
            return False
        
        # 5. Test session expiry
        print("5. Testing session expiry...")
        try:
            if not retrieved_session.is_expired():
                print(f"   ✅ Session is not expired (correct)")
            else:
                print(f"   ❌ Session is expired (incorrect)")
                return False
        except Exception as e:
            print(f"   ❌ Error checking session expiry: {e}")
            return False
        
        # 6. Test session invalidation
        print("6. Testing session invalidation...")
        try:
            retrieved_session.invalidate()
            db.session.commit()
            
            if not retrieved_session.is_valid():
                print(f"   ✅ Session invalidated correctly")
            else:
                print(f"   ❌ Session still valid after invalidation")
                return False
        except Exception as e:
            print(f"   ❌ Error invalidating session: {e}")
            return False
        
        # 7. Test cleanup
        print("7. Testing session cleanup...")
        try:
            cleanup_count = Session.cleanup_expired_sessions()
            print(f"   ✅ Cleaned up {cleanup_count} expired sessions")
        except Exception as e:
            print(f"   ❌ Error during cleanup: {e}")
            return False
        
        # 8. Test user password migration
        print("8. Testing bcrypt password functionality...")
        try:
            # Get a user
            user = User.query.first()
            if user:
                # Check password info
                password_info = user.get_password_strength_info()
                print(f"   ✅ Password type: {password_info['type']}")
                print(f"   ✅ Password strength: {password_info['strength']}")
                
                # Test password checking
                if user.has_password():
                    print(f"   ✅ User has password set")
                else:
                    print(f"   ⚠️  User has no password set")
            else:
                print(f"   ⚠️  No users found in database")
        except Exception as e:
            print(f"   ❌ Error testing password functionality: {e}")
            return False
        
        print("\n=== All Tests Passed! ===")
        return True

if __name__ == '__main__':
    success = test_database_sessions()
    if success:
        print("\n🎉 Database session management is working correctly!")
    else:
        print("\n❌ Database session management has issues.")
