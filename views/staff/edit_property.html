{% extends "layout.html" %}

{% block title %}Edit Property - Green Peak Shelters Portal{% endblock %}
{% block header_title %}Edit Property{% endblock %}
{% block header_subtitle %}Update property information{% endblock %}

{% block content %}
<div class="bg-white shadow-md rounded-lg p-6 max-w-2xl mx-auto">
    <h1 class="text-2xl font-bold text-primary-700 mb-6">Edit Property: {{ property.name }}</h1>
    
    {% with messages = get_flashed_messages(with_categories=true) %}
        {% if messages %}
            {% for category, message in messages %}
                <div class="p-4 mb-4 {% if category == 'error' %}bg-red-100 text-red-700{% else %}bg-green-100 text-green-700{% endif %} rounded">
                    {{ message }}
                </div>
            {% endfor %}
        {% endif %}
    {% endwith %}
    
    <form method="POST">
        <div class="mb-4">
            <label for="name" class="block text-gray-700 font-medium mb-2">Property Name</label>
            <input type="text" id="name" name="name" value="{{ property.name }}" required
                class="w-full px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500">
        </div>
        
        <div class="mb-4">
            <label for="type" class="block text-gray-700 font-medium mb-2">Property Type</label>
            <select id="type" name="type" required
                class="w-full px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500">
                <option value="Residential" {% if property.property_type == 'Residential' %}selected{% endif %}>Residential</option>
                <option value="Commercial" {% if property.property_type == 'Commercial' %}selected{% endif %}>Commercial</option>
                <option value="Industrial" {% if property.property_type == 'Industrial' %}selected{% endif %}>Industrial</option>
                <option value="Land" {% if property.property_type == 'Land' %}selected{% endif %}>Land</option>
            </select>
        </div>
        
        <div class="mb-4">
            <label for="location" class="block text-gray-700 font-medium mb-2">Location</label>
            <input type="text" id="location" name="location" value="{{ property.location }}" required
                class="w-full px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500">
        </div>
        
        <div class="mb-4">
            <label for="price" class="block text-gray-700 font-medium mb-2">Price (₦)</label>
            <input type="number" id="price" name="price" value="{{ property.price }}" required min="0" step="0.01"
                class="w-full px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500">
        </div>
        
        <div class="mb-4">
            <label for="payment_cadence" class="block text-gray-700 font-medium mb-2">Payment Cadence</label>
            <select id="payment_cadence" name="payment_cadence" required
                class="w-full px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500">
                <option value="one_time" {% if property.payment_cadence == 'one_time' %}selected{% endif %}>One Time</option>
                <option value="monthly" {% if property.payment_cadence == 'monthly' %}selected{% endif %}>Monthly</option>
                <option value="quarterly" {% if property.payment_cadence == 'quarterly' %}selected{% endif %}>Quarterly</option>
                <option value="half_year" {% if property.payment_cadence == 'half_year' %}selected{% endif %}>Half Year</option>
                <option value="annual" {% if property.payment_cadence == 'annual' %}selected{% endif %}>Annual</option>
                <option value="biennial" {% if property.payment_cadence == 'biennial' %}selected{% endif %}>Biennial</option>
            </select>
        </div>
        
        <div class="mb-4">
            <label for="status" class="block text-gray-700 font-medium mb-2">Status</label>
            <select id="status" name="status" required
                class="w-full px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500">
                <option value="Available" {% if property.status == 'Available' %}selected{% endif %}>Available</option>
                <option value="Assigned" {% if property.status == 'Assigned' %}selected{% endif %}>Assigned</option>
                <option value="Sold" {% if property.status == 'Sold' %}selected{% endif %}>Sold</option>
                <option value="Rented" {% if property.status == 'Rented' %}selected{% endif %}>Rented</option>
                <option value="Under Maintenance" {% if property.status == 'Under Maintenance' %}selected{% endif %}>Under Maintenance</option>
            </select>
        </div>
        
        <div class="mb-4">
            <label for="description" class="block text-gray-700 font-medium mb-2">Description</label>
            <textarea id="description" name="description" rows="4"
                class="w-full px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500">{{ property.description }}</textarea>
        </div>
        
        <div class="flex justify-between mt-6">
            <a href="{{ url_for('staff.property_detail', id=property.id) }}" class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded">
                Cancel
            </a>
            <button type="submit" class="bg-primary-600 hover:bg-primary-700 text-white font-bold py-2 px-4 rounded">
                Update Property
            </button>
        </div>
    </form>
</div>
{% endblock %}