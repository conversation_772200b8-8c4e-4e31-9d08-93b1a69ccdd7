
{% extends "layout.html" %}

{% block title %}Client Details - Green Peak Shelters Portal{% endblock %}
{% block header_title %}Client Details{% endblock %}
{% block header_subtitle %}View and manage client information{% endblock %}

{% block content %}
<div class="mb-6">
    <h1 class="text-2xl font-bold text-primary-700">Client Profile: {{ client.full_name }}</h1>
    <p class="text-gray-600">Client ID: {{ client.id }}</p>
</div>

{% with messages = get_flashed_messages(with_categories=true) %}
    {% if messages %}
        {% for category, message in messages %}
            <div class="p-4 mb-4 {% if category == 'error' %}bg-red-100 text-red-700{% else %}bg-green-100 text-green-700{% endif %} rounded">
                {{ message }}
            </div>
        {% endfor %}
    {% endif %}
{% endwith %}

<div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
    <!-- Client Information -->
    <div class="bg-white shadow-md rounded-lg p-6 col-span-1">
        <h2 class="text-xl font-bold text-green-700 mb-4">Personal Information</h2>
        <div class="space-y-3">
            <div>
                <p class="text-gray-600 text-sm">Full Name</p>
                <p class="font-medium">{{ client.full_name }}</p>
            </div>
            <div>
                <p class="text-gray-600 text-sm">Email</p>
                <p class="font-medium">{{ client.email }}</p>
            </div>
            <div>
                <p class="text-gray-600 text-sm">Phone</p>
                <p class="font-medium">{{ client.phone or 'Not provided' }}</p>
            </div>
            <div>
                <p class="text-gray-600 text-sm">Address</p>
                <p class="font-medium">{{ client.address or 'Not provided' }}</p>
            </div>
            <div>
                <p class="text-gray-600 text-sm">Status</p>
                <p class="font-medium">
                    {% if client.active %}
                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">Active</span>
                    {% else %}
                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">Inactive</span>
                    {% endif %}
                </p>
            </div>
        </div>
        <div class="mt-6">
            <a href="#" class="text-indigo-600 hover:text-indigo-900">Edit Information</a>
        </div>
    </div>
    
    <!-- Client Actions -->
    <div class="bg-white shadow-md rounded-lg p-6 col-span-2">
        <h2 class="text-xl font-bold text-green-700 mb-4">Quick Actions</h2>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <a href="{{ url_for('staff.assign_property', client_id=client.id) }}" class="bg-green-600 hover:bg-green-700 text-white font-bold py-2 px-4 rounded text-center">
                Assign Property
            </a>
            <a href="{{ url_for('staff.assign_service', client_id=client.id) }}" class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded text-center">
                Assign Service
            </a>
            <a href="#" class="bg-purple-600 hover:bg-purple-700 text-white font-bold py-2 px-4 rounded text-center">
                Record Payment
            </a>
            <a href="#" class="bg-gray-600 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded text-center">
                Send Message
            </a>
        </div>
    </div>
</div>

<!-- Properties Section -->
<div class="bg-white shadow-md rounded-lg overflow-hidden mb-8">
    <div class="px-6 py-4 bg-gray-50 border-b border-gray-200 flex justify-between items-center">
        <h2 class="text-xl font-bold text-green-700">Properties</h2>
        <a href="{{ url_for('staff.assign_property', client_id=client.id) }}" class="bg-green-600 hover:bg-green-700 text-white text-sm font-bold py-1 px-3 rounded">
            + Assign Property
        </a>
    </div>
    
    {% if properties %}
        <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
                <tr>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Property Name</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Location</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Price</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
                {% for property in properties %}
                    <tr>
                        <td class="px-6 py-4 whitespace-nowrap">{{ property.name }}</td>
                        <td class="px-6 py-4 whitespace-nowrap">{{ property.property_type }}</td>
                        <td class="px-6 py-4 whitespace-nowrap">{{ property.location }}</td>
                        <td class="px-6 py-4 whitespace-nowrap">₦{{ property.price }}</td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full 
                                {% if property.status == 'Available' %}bg-green-100 text-green-800
                                {% elif property.status == 'Sold' %}bg-red-100 text-red-800
                                {% else %}bg-yellow-100 text-yellow-800{% endif %}">
                                {{ property.status }}
                            </span>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                            <a href="#" class="text-red-600 hover:text-red-900">Remove</a>
                        </td>
                    </tr>
                {% endfor %}
            </tbody>
        </table>
    {% else %}
        <div class="px-6 py-4 text-center text-gray-500">
            No properties assigned to this client.
        </div>
    {% endif %}
</div>

<!-- Services Section -->
<div class="bg-white shadow-md rounded-lg overflow-hidden mb-8">
    <div class="px-6 py-4 bg-gray-50 border-b border-gray-200 flex justify-between items-center">
        <h2 class="text-xl font-bold text-green-700">Services</h2>
        <a href="{{ url_for('staff.assign_service', client_id=client.id) }}" class="bg-blue-600 hover:bg-blue-700 text-white text-sm font-bold py-1 px-3 rounded">
            + Assign Service
        </a>
    </div>
    
    {% if services %}
        <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
                <tr>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Service Name</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Price</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
                {% for service in services %}
                    <tr>
                        <td class="px-6 py-4 whitespace-nowrap">{{ service.name }}</td>
                        <td class="px-6 py-4 whitespace-nowrap">{{ service.service_type }}</td>
                        <td class="px-6 py-4 whitespace-nowrap">{{ service.description }}</td>
                        <td class="px-6 py-4 whitespace-nowrap">₦{{ service.price }}</td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full 
                                {% if service.status == 'Active' %}bg-green-100 text-green-800
                                {% else %}bg-red-100 text-red-800{% endif %}">
                                {{ service.status }}
                            </span>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                            <a href="#" class="text-red-600 hover:text-red-900">Remove</a>
                        </td>
                    </tr>
                {% endfor %}
            </tbody>
        </table>
    {% else %}
        <div class="px-6 py-4 text-center text-gray-500">
            No services assigned to this client.
        </div>
    {% endif %}
</div>

<!-- Payment History Section -->
<div class="bg-white shadow-md rounded-lg overflow-hidden mb-8">
    <div class="px-6 py-4 bg-gray-50 border-b border-gray-200">
        <h2 class="text-xl font-bold text-green-700">Payment History</h2>
    </div>
    
    {% if payments %}
        <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
                <tr>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Item</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Amount</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
                {% for payment in payments %}
                    <tr>
                        <td class="px-6 py-4 whitespace-nowrap">{{ payment.payment_date.strftime('%Y-%m-%d') }}</td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            {% if payment.item_type == 'property' %}
                                {{ payment.related_item.name if payment.related_item else 'Unknown Property' }}
                            {% else %}
                                {{ payment.related_item.name if payment.related_item else 'Unknown Service' }}
                            {% endif %}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">{{ payment.item_type|capitalize }}</td>
                        <td class="px-6 py-4 whitespace-nowrap">₦{{ payment.amount }}</td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full 
                                {% if payment.status == 'Completed' %}bg-green-100 text-green-800
                                {% elif payment.status == 'Pending' %}bg-yellow-100 text-yellow-800
                                {% elif payment.status == 'Partial' %}bg-blue-100 text-blue-800
                                {% else %}bg-red-100 text-red-800{% endif %}">
                                {{ payment.status }}
                            </span>
                        </td>
                    </tr>
                {% endfor %}
            </tbody>
        </table>
    {% else %}
        <div class="px-6 py-4 text-center text-gray-500">
            No payment history for this client.
        </div>
    {% endif %}
</div>

<div class="mt-4">
    <a href="{{ url_for('staff.client_list') }}" class="text-primary-600 hover:text-primary-800">
        &larr; Back to Client List
    </a>
</div>
{% endblock %}
