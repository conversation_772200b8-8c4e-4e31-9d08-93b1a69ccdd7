{% extends "layout.html" %}

{% block title %}Create Property - Green Peak Shelters Portal{% endblock %}
{% block header_title %}Create Property{% endblock %}
{% block header_subtitle %}Add a new property to the system{% endblock %}

{% block content %}
<div class="bg-white shadow-md rounded-lg p-6 max-w-2xl mx-auto">
    <h1 class="text-2xl font-bold text-primary-700 mb-6">Add New Property</h1>
    
    {% with messages = get_flashed_messages(with_categories=true) %}
        {% if messages %}
            {% for category, message in messages %}
                <div class="p-4 mb-4 {% if category == 'error' %}bg-red-100 text-red-700{% else %}bg-green-100 text-green-700{% endif %} rounded">
                    {{ message }}
                </div>
            {% endfor %}
        {% endif %}
    {% endwith %}
    
    <form method="POST" action="{{ url_for('staff.create_property') }}">
        <div class="mb-4">
            <label for="name" class="block text-gray-700 font-medium mb-2">Property Name</label>
            <input type="text" id="name" name="name" required
                class="w-full px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500">
        </div>
        
        <div class="mb-4">
            <label for="type" class="block text-gray-700 font-medium mb-2">Property Type</label>
            <select id="type" name="type" required
                class="w-full px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500">
                <option value="">Select a type</option>
                <option value="Residential (Rented)">Residential (Rented)</option>
                <option value="Commercial (Rented)">Commercial (Rented)</option>
                <option value="Residential (Owned)">Residential (Owned)</option>
                <option value="Commercial (Owned)">Commercial (Owned)</option>
            </select>
        </div>
        
        <div class="mb-4">
            <label for="location" class="block text-gray-700 font-medium mb-2">Location (Address)</label>
            <textarea id="location" name="location" rows="3" required
                class="w-full px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"></textarea>
        </div>
        
        <div class="mb-4">
            <label for="price" class="block text-gray-700 font-medium mb-2">Price (₦)</label>
            <input type="number" id="price" name="price" required
                class="w-full px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500">
        </div>
        
        <div class="mb-4">
            <label for="description" class="block text-gray-700 font-medium mb-2">Description</label>
            <textarea id="description" name="description" rows="4"
                class="w-full px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"></textarea>
        </div>
        
        <div class="mb-4">
            <label for="payment_cadence" class="block text-gray-700 font-medium mb-2">Payment Cadence</label>
            <select id="payment_cadence" name="payment_cadence" required
                class="w-full px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500">
                <option value="one_time">One Time</option>
                <option value="monthly">Monthly</option>
                <option value="quarterly">Quarterly</option>
                <option value="half_year">Half-Yearly</option>
                <option value="annual">Annual</option>
                <option value="biennial">Biennial (Every 2 Years)</option>
            </select>
        </div>
        
        <div class="flex justify-between mt-6">
            <a href="{{ url_for('staff.property_list') }}" class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded">
                Cancel
            </a>
            <button type="submit" class="bg-primary-600 hover:bg-primary-700 text-white font-bold py-2 px-4 rounded">
                Create Property
            </button>
        </div>
    </form>
</div>
{% endblock %}
