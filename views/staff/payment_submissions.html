{% extends "layout.html" %}

{% block title %}Payment Submissions - Green Peak Shelters Portal{% endblock %}
{% block header_title %}Payment Submissions{% endblock %}
{% block header_subtitle %}Review client payment submissions{% endblock %}

{% block content %}
<div class="flex justify-between items-center mb-6">
    <h1 class="text-2xl font-bold text-primary-700">Payment Submissions</h1>
    <div class="flex space-x-2">
        <span class="bg-yellow-100 text-yellow-800 px-3 py-1 rounded-full text-sm font-medium">
            {{ submissions|selectattr('status', 'equalto', 'pending')|list|length }} Pending Review
        </span>
    </div>
</div>

<!-- Filter Tabs -->
<div class="mb-6">
    <div class="border-b border-gray-200">
        <nav class="-mb-px flex space-x-8">
            <a href="#" onclick="filterSubmissions('all')" class="filter-tab active border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm">
                All Submissions ({{ submissions|length }})
            </a>
            <a href="#" onclick="filterSubmissions('pending')" class="filter-tab border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm">
                Pending ({{ submissions|selectattr('status', 'equalto', 'pending')|list|length }})
            </a>
            <a href="#" onclick="filterSubmissions('approved')" class="filter-tab border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm">
                Approved ({{ submissions|selectattr('status', 'equalto', 'approved')|list|length }})
            </a>
            <a href="#" onclick="filterSubmissions('rejected')" class="filter-tab border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm">
                Rejected ({{ submissions|selectattr('status', 'equalto', 'rejected')|list|length }})
            </a>
        </nav>
    </div>
</div>

<!-- Submissions Table -->
<div class="bg-white shadow-md rounded-lg overflow-hidden">
    <table class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
            <tr>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">ID</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Client</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Item</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Amount</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Payment Date</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Method</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Submitted</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
            {% if submissions %}
                {% for submission in submissions %}
                    <tr class="submission-row" data-status="{{ submission.status }}">
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                            #{{ submission.id }}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm font-medium text-gray-900">{{ submission.client.full_name }}</div>
                            <div class="text-sm text-gray-500">{{ submission.client.email }}</div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm font-medium text-gray-900">{{ submission.get_item_name() }}</div>
                            <div class="text-sm text-gray-500">{{ submission.item_type|title }}</div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                            ₦{{ "{:,.2f}".format(submission.amount) }}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            {{ submission.payment_date.strftime('%Y-%m-%d') }}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            {{ submission.payment_method.replace('_', ' ')|title }}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full {{ submission.get_status_badge_class() }}">
                                {{ submission.status|title }}
                            </span>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {{ submission.created_at.strftime('%Y-%m-%d %H:%M') }}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                            <div class="flex space-x-2">
                                <a href="{{ url_for('staff.payment_submission_detail', submission_id=submission.id) }}" 
                                   class="text-indigo-600 hover:text-indigo-900">View</a>
                                {% if submission.status == 'pending' %}
                                    <span class="text-gray-300">|</span>
                                    <button onclick="quickApprove({{ submission.id }})" 
                                            class="text-green-600 hover:text-green-900">Approve</button>
                                    <span class="text-gray-300">|</span>
                                    <button onclick="quickReject({{ submission.id }})" 
                                            class="text-red-600 hover:text-red-900">Reject</button>
                                {% endif %}
                            </div>
                        </td>
                    </tr>
                {% endfor %}
            {% else %}
                <tr>
                    <td colspan="9" class="px-6 py-4 text-center text-gray-500">No payment submissions found.</td>
                </tr>
            {% endif %}
        </tbody>
    </table>
</div>

<!-- Quick Action Modals -->
<div id="quickApproveModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden">
    <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
        <div class="mt-3 text-center">
            <h3 class="text-lg font-medium text-gray-900">Approve Payment Submission</h3>
            <div class="mt-2 px-7 py-3">
                <p class="text-sm text-gray-500">Are you sure you want to approve this payment submission?</p>
                <form id="approveForm" method="POST" class="mt-4">
                    <textarea name="review_notes" placeholder="Optional approval notes..." 
                              class="w-full px-3 py-2 border border-gray-300 rounded-md text-sm"></textarea>
                </form>
            </div>
            <div class="items-center px-4 py-3">
                <button onclick="submitApproval()" 
                        class="px-4 py-2 bg-green-500 text-white text-base font-medium rounded-md shadow-sm hover:bg-green-600 mr-2">
                    Approve
                </button>
                <button onclick="closeModal('quickApproveModal')" 
                        class="px-4 py-2 bg-gray-300 text-gray-800 text-base font-medium rounded-md shadow-sm hover:bg-gray-400">
                    Cancel
                </button>
            </div>
        </div>
    </div>
</div>

<div id="quickRejectModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden">
    <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
        <div class="mt-3 text-center">
            <h3 class="text-lg font-medium text-gray-900">Reject Payment Submission</h3>
            <div class="mt-2 px-7 py-3">
                <p class="text-sm text-gray-500">Please provide a reason for rejection:</p>
                <form id="rejectForm" method="POST" class="mt-4">
                    <textarea name="review_notes" placeholder="Reason for rejection..." required
                              class="w-full px-3 py-2 border border-gray-300 rounded-md text-sm"></textarea>
                </form>
            </div>
            <div class="items-center px-4 py-3">
                <button onclick="submitRejection()" 
                        class="px-4 py-2 bg-red-500 text-white text-base font-medium rounded-md shadow-sm hover:bg-red-600 mr-2">
                    Reject
                </button>
                <button onclick="closeModal('quickRejectModal')" 
                        class="px-4 py-2 bg-gray-300 text-gray-800 text-base font-medium rounded-md shadow-sm hover:bg-gray-400">
                    Cancel
                </button>
            </div>
        </div>
    </div>
</div>

<script>
let currentSubmissionId = null;

function filterSubmissions(status) {
    const rows = document.querySelectorAll('.submission-row');
    const tabs = document.querySelectorAll('.filter-tab');
    
    // Update active tab
    tabs.forEach(tab => {
        tab.classList.remove('active', 'border-indigo-500', 'text-indigo-600');
        tab.classList.add('border-transparent', 'text-gray-500');
    });
    event.target.classList.add('active', 'border-indigo-500', 'text-indigo-600');
    event.target.classList.remove('border-transparent', 'text-gray-500');
    
    // Filter rows
    rows.forEach(row => {
        if (status === 'all' || row.dataset.status === status) {
            row.style.display = '';
        } else {
            row.style.display = 'none';
        }
    });
}

function quickApprove(submissionId) {
    currentSubmissionId = submissionId;
    document.getElementById('quickApproveModal').classList.remove('hidden');
}

function quickReject(submissionId) {
    currentSubmissionId = submissionId;
    document.getElementById('quickRejectModal').classList.remove('hidden');
}

function closeModal(modalId) {
    document.getElementById(modalId).classList.add('hidden');
    currentSubmissionId = null;
}

function submitApproval() {
    if (currentSubmissionId) {
        const form = document.getElementById('approveForm');
        form.action = `/staff/payment-submissions/${currentSubmissionId}/approve`;
        form.submit();
    }
}

function submitRejection() {
    if (currentSubmissionId) {
        const form = document.getElementById('rejectForm');
        const notes = form.querySelector('textarea').value.trim();
        if (!notes) {
            alert('Please provide a reason for rejection.');
            return;
        }
        form.action = `/staff/payment-submissions/${currentSubmissionId}/reject`;
        form.submit();
    }
}

// Close modals when clicking outside
window.onclick = function(event) {
    const approveModal = document.getElementById('quickApproveModal');
    const rejectModal = document.getElementById('quickRejectModal');
    if (event.target === approveModal) {
        closeModal('quickApproveModal');
    }
    if (event.target === rejectModal) {
        closeModal('quickRejectModal');
    }
}
</script>

<style>
.filter-tab.active {
    border-color: #4f46e5 !important;
    color: #4f46e5 !important;
}
</style>
{% endblock %}
