{% extends "layout.html" %}

{% block title %}Property List - Green Peak Shelters Portal{% endblock %}
{% block header_title %}Property Management{% endblock %}
{% block header_subtitle %}View and manage properties{% endblock %}

{% block content %}
<div class="flex justify-between items-center mb-6">
    <h1 class="text-2xl font-bold text-primary-700">Property List</h1>
    <a href="{{ url_for('staff.create_property') }}" class="bg-primary-600 hover:bg-primary-700 text-white font-bold py-2 px-4 rounded">
        Add New Property
    </a>
</div>
        
<table class="min-w-full bg-white">
    <thead>
        <tr>
            <th class="py-3 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
            <th class="py-3 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
            <th class="py-3 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Price</th>
            <th class="py-3 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
            <th class="py-3 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Payment Status</th>
            <th class="py-3 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
        </tr>
    </thead>
    <tbody>
        {% for property in properties %}
        <tr>
            <td class="py-4 px-4 border-b border-gray-200">{{ property.name }}</td>
            <td class="py-4 px-4 border-b border-gray-200">{{ property.property_type }}</td>
            <td class="py-4 px-4 border-b border-gray-200">₦{{ property.price|format_currency }}</td>
            <td class="py-4 px-4 border-b border-gray-200">
                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full 
                    {% if property.status == 'Available' %}bg-green-100 text-green-800
                    {% elif property.status == 'Sold' %}bg-blue-100 text-blue-800
                    {% elif property.status == 'Rented' %}bg-purple-100 text-purple-800
                    {% else %}bg-gray-100 text-gray-800{% endif %}">
                    {{ property.status }}
                </span>
            </td>
            <td class="py-4 px-4 border-b border-gray-200">
                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full 
                    {% if property.get_payment_status() == 'Up to Date' %}bg-green-100 text-green-800
                    {% elif property.get_payment_status().startswith('Overdue') %}bg-red-100 text-red-800
                    {% elif property.get_payment_status() == 'Never Paid' %}bg-yellow-100 text-yellow-800
                    {% else %}bg-gray-100 text-gray-800{% endif %}">
                    {{ property.get_payment_status() }}
                </span>
            </td>
            <td class="py-4 px-4 border-b border-gray-200">
                <a href="{{ url_for('staff.property_detail', id=property.id) }}" class="text-primary-600 hover:text-primary-900 mr-3">View</a>
                <a href="{{ url_for('staff.edit_property', id=property.id) }}" class="text-primary-600 hover:text-primary-900">Edit</a>
            </td>
        </tr>
        {% endfor %}
    </tbody>
</table>
        
<div class="mt-4">
    <a href="{{ url_for('staff.dashboard') }}" class="text-primary-600 hover:text-primary-800">
        &larr; Back to Dashboard
    </a>
</div>
{% endblock %}
