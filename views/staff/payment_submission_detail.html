{% extends "layout.html" %}

{% block title %}Payment Submission #{{ submission.id }} - Green Peak Shelters Portal{% endblock %}
{% block header_title %}Payment Submission #{{ submission.id }}{% endblock %}
{% block header_subtitle %}Review payment submission details{% endblock %}

{% block content %}
<div class="max-w-6xl mx-auto">
    <!-- Header with Status -->
    <div class="bg-white shadow-lg rounded-lg overflow-hidden mb-6">
        <div class="px-6 py-4 border-b border-gray-200">
            <div class="flex justify-between items-center">
                <div>
                    <h1 class="text-2xl font-bold text-gray-900">Payment Submission #{{ submission.id }}</h1>
                    <p class="text-gray-600">Submitted {{ submission.created_at.strftime('%B %d, %Y at %I:%M %p') }}</p>
                </div>
                <div class="flex items-center space-x-4">
                    <span class="px-3 py-1 text-sm font-semibold rounded-full {{ submission.get_status_badge_class() }}">
                        {{ submission.status|title }}
                    </span>
                    {% if submission.status == 'pending' %}
                        <div class="flex space-x-2">
                            <button onclick="showApproveModal()" 
                                    class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-md text-sm font-medium">
                                Approve
                            </button>
                            <button onclick="showRejectModal()"
                                    class="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-md text-sm font-medium">
                                Mark as Failed
                            </button>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Main Content Grid -->
        <div class="p-6">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                <!-- Left Column: Payment Details -->
                <div class="space-y-6">
                    <!-- Client Information -->
                    <div class="bg-gray-50 p-4 rounded-lg">
                        <h3 class="text-lg font-semibold text-gray-700 mb-3">Client Information</h3>
                        <div class="space-y-2">
                            <div class="flex justify-between">
                                <span class="font-medium text-gray-600">Name:</span>
                                <span class="text-gray-800">{{ submission.client.full_name }}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="font-medium text-gray-600">Email:</span>
                                <span class="text-gray-800">{{ submission.client.email }}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="font-medium text-gray-600">Phone:</span>
                                <span class="text-gray-800">{{ submission.client.phone or 'N/A' }}</span>
                            </div>
                        </div>
                    </div>

                    <!-- Payment Information -->
                    <div class="bg-gray-50 p-4 rounded-lg">
                        <h3 class="text-lg font-semibold text-gray-700 mb-3">Payment Information</h3>
                        <div class="space-y-2">
                            <div class="flex justify-between">
                                <span class="font-medium text-gray-600">Item:</span>
                                <span class="text-gray-800">{{ submission.get_item_name() }}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="font-medium text-gray-600">Type:</span>
                                <span class="text-gray-800">{{ submission.item_type|title }}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="font-medium text-gray-600">Amount:</span>
                                <span class="text-gray-800 font-bold text-lg">₦{{ "{:,.2f}".format(submission.amount) }}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="font-medium text-gray-600">Payment Date:</span>
                                <span class="text-gray-800">{{ submission.payment_date.strftime('%B %d, %Y') }}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="font-medium text-gray-600">Payment Method:</span>
                                <span class="text-gray-800">{{ submission.payment_method.replace('_', ' ')|title }}</span>
                            </div>
                            {% if submission.reference_number %}
                                <div class="flex justify-between">
                                    <span class="font-medium text-gray-600">Reference:</span>
                                    <span class="text-gray-800">{{ submission.reference_number }}</span>
                                </div>
                            {% endif %}
                        </div>
                    </div>

                    <!-- Additional Notes -->
                    {% if submission.description %}
                        <div class="bg-gray-50 p-4 rounded-lg">
                            <h3 class="text-lg font-semibold text-gray-700 mb-3">Client Notes</h3>
                            <p class="text-gray-800">{{ submission.description }}</p>
                        </div>
                    {% endif %}

                    <!-- Review Information -->
                    {% if submission.status != 'pending' %}
                        <div class="bg-gray-50 p-4 rounded-lg">
                            <h3 class="text-lg font-semibold text-gray-700 mb-3">Review Information</h3>
                            <div class="space-y-2">
                                <div class="flex justify-between">
                                    <span class="font-medium text-gray-600">Reviewed by:</span>
                                    <span class="text-gray-800">{{ submission.reviewer.full_name if submission.reviewer else 'N/A' }}</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="font-medium text-gray-600">Review Date:</span>
                                    <span class="text-gray-800">{{ submission.reviewed_at.strftime('%B %d, %Y at %I:%M %p') if submission.reviewed_at else 'N/A' }}</span>
                                </div>
                                {% if submission.review_notes %}
                                    <div class="mt-3">
                                        <span class="font-medium text-gray-600">Review Notes:</span>
                                        <p class="text-gray-800 mt-1">{{ submission.review_notes }}</p>
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                    {% endif %}
                </div>

                <!-- Right Column: Proof of Payment -->
                <div>
                    <div class="bg-gray-50 p-4 rounded-lg">
                        <h3 class="text-lg font-semibold text-gray-700 mb-3">Proof of Payment</h3>
                        {% if submission.has_proof_attachment() %}
                            <div class="text-center">
                                {% if submission.is_image_proof() %}
                                    <!-- Image Preview -->
                                    <div class="mb-4">
                                        <img src="{{ url_for('staff.view_payment_proof', submission_id=submission.id) }}" 
                                             alt="Payment Proof" 
                                             class="max-w-full h-auto rounded-lg shadow-md mx-auto"
                                             style="max-height: 400px;">
                                    </div>
                                {% elif submission.is_pdf_proof() %}
                                    <!-- PDF Preview -->
                                    <div class="mb-4">
                                        <div class="bg-red-100 p-8 rounded-lg">
                                            <svg class="mx-auto h-16 w-16 text-red-600 mb-4" fill="currentColor" viewBox="0 0 20 20">
                                                <path fill-rule="evenodd" d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z" clip-rule="evenodd"></path>
                                            </svg>
                                            <p class="text-red-800 font-medium">PDF Document</p>
                                            <p class="text-red-600 text-sm">{{ submission.proof_filename }}</p>
                                        </div>
                                    </div>
                                {% else %}
                                    <!-- Other File Type -->
                                    <div class="mb-4">
                                        <div class="bg-gray-100 p-8 rounded-lg">
                                            <svg class="mx-auto h-16 w-16 text-gray-600 mb-4" fill="currentColor" viewBox="0 0 20 20">
                                                <path fill-rule="evenodd" d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4z" clip-rule="evenodd"></path>
                                            </svg>
                                            <p class="text-gray-800 font-medium">File Attachment</p>
                                            <p class="text-gray-600 text-sm">{{ submission.proof_filename }}</p>
                                        </div>
                                    </div>
                                {% endif %}
                                
                                <!-- Download/View Button -->
                                <a href="{{ url_for('staff.view_payment_proof', submission_id=submission.id) }}" 
                                   target="_blank"
                                   class="inline-flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white text-sm font-medium rounded-md">
                                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                                    </svg>
                                    View Full Size
                                </a>
                            </div>
                        {% else %}
                            <p class="text-gray-500 text-center">No proof of payment attached.</p>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Navigation -->
    <div class="flex justify-between items-center">
        <a href="{{ url_for('staff.payment_submissions') }}" 
           class="text-primary-600 hover:text-primary-800">
            ← Back to Payment Submissions
        </a>
        
        <a href="{{ url_for('staff.client_detail', client_id=submission.client_id) }}" 
           class="bg-primary-600 hover:bg-primary-700 text-white font-bold py-2 px-4 rounded">
            View Client Details
        </a>
    </div>
</div>

<!-- Approve Modal -->
<div id="approveModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden">
    <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
        <div class="mt-3">
            <h3 class="text-lg font-medium text-gray-900 text-center">Approve Payment Submission</h3>
            <div class="mt-4">
                <p class="text-sm text-gray-500 text-center mb-4">
                    This will create a payment record and mark the submission as approved.
                </p>
                <form id="approveForm" method="POST" action="{{ url_for('staff.approve_payment_submission', submission_id=submission.id) }}">
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 mb-2">Payment Status *</label>
                        <select name="payment_status"
                                class="w-full px-3 py-2 border border-gray-300 rounded-md text-sm"
                                required>
                            <option value="">Select payment status...</option>
                            <option value="partial">Partial Payment</option>
                            <option value="completed">Completed Payment</option>
                        </select>
                    </div>
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 mb-2">Approval Notes (Optional)</label>
                        <textarea name="review_notes"
                                  placeholder="Add any notes about this approval..."
                                  class="w-full px-3 py-2 border border-gray-300 rounded-md text-sm"
                                  rows="3"></textarea>
                    </div>
                </form>
            </div>
            <div class="items-center px-4 py-3 text-center">
                <button type="button" onclick="submitApprove()"
                        class="px-4 py-2 bg-green-500 text-white text-base font-medium rounded-md shadow-sm hover:bg-green-600 mr-2">
                    Approve Payment
                </button>
                <button type="button" onclick="closeModal('approveModal')"
                        class="px-4 py-2 bg-gray-300 text-gray-800 text-base font-medium rounded-md shadow-sm hover:bg-gray-400">
                    Cancel
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Reject Modal -->
<div id="rejectModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden">
    <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
        <div class="mt-3">
            <h3 class="text-lg font-medium text-gray-900 text-center">Mark Payment as Failed</h3>
            <div class="mt-4">
                <p class="text-sm text-gray-500 text-center mb-4">
                    This will mark the payment as failed and create a failed payment record. Please provide a reason.
                </p>
                <form id="rejectForm" method="POST" action="{{ url_for('staff.reject_payment_submission', submission_id=submission.id) }}">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Reason for Rejection *</label>
                    <textarea name="review_notes" 
                              placeholder="Explain why this payment is being rejected..." 
                              class="w-full px-3 py-2 border border-gray-300 rounded-md text-sm"
                              rows="3" required></textarea>
                </form>
            </div>
            <div class="items-center px-4 py-3 text-center">
                <button type="button" onclick="submitReject()"
                        class="px-4 py-2 bg-red-500 text-white text-base font-medium rounded-md shadow-sm hover:bg-red-600 mr-2">
                    Mark as Failed
                </button>
                <button type="button" onclick="closeModal('rejectModal')"
                        class="px-4 py-2 bg-gray-300 text-gray-800 text-base font-medium rounded-md shadow-sm hover:bg-gray-400">
                    Cancel
                </button>
            </div>
        </div>
    </div>
</div>

<script>
function showApproveModal() {
    document.getElementById('approveModal').classList.remove('hidden');
}

function showRejectModal() {
    document.getElementById('rejectModal').classList.remove('hidden');
}

function closeModal(modalId) {
    document.getElementById(modalId).classList.add('hidden');
}

function submitApprove() {
    const form = document.getElementById('approveForm');
    const paymentStatus = form.querySelector('select[name="payment_status"]').value;
    if (!paymentStatus) {
        alert('Please select a payment status.');
        return;
    }
    form.submit();
}

function submitReject() {
    const form = document.getElementById('rejectForm');
    const notes = form.querySelector('textarea').value.trim();
    if (!notes) {
        alert('Please provide a reason for rejection.');
        return;
    }
    form.submit();
}

// Close modals when clicking outside
window.onclick = function(event) {
    const approveModal = document.getElementById('approveModal');
    const rejectModal = document.getElementById('rejectModal');
    if (event.target === approveModal) {
        closeModal('approveModal');
    }
    if (event.target === rejectModal) {
        closeModal('rejectModal');
    }
}
</script>
{% endblock %}
