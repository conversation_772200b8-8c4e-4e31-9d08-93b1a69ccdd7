{% extends "layout.html" %}

{% block title %}Property Details - Green Peak Shelters Portal{% endblock %}
{% block header_title %}Property Details{% endblock %}
{% block header_subtitle %}View and manage property information{% endblock %}

{% block content %}
<div class="mb-6">
    <h1 class="text-2xl font-bold text-primary-700">Property: {{ property.name }}</h1>
    <p class="text-gray-600">Property ID: {{ property.id }}</p>
</div>

{% with messages = get_flashed_messages(with_categories=true) %}
    {% if messages %}
        {% for category, message in messages %}
            <div class="p-4 mb-4 {% if category == 'error' %}bg-red-100 text-red-700{% else %}bg-green-100 text-green-700{% endif %} rounded">
                {{ message }}
            </div>
        {% endfor %}
    {% endif %}
{% endwith %}

<div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
    <!-- Property Details -->
    <div class="bg-white shadow-md rounded-lg p-6">
        <h2 class="text-xl font-bold text-green-700 mb-4">Property Details</h2>
        <div class="space-y-3">
            <div>
                <span class="font-semibold text-gray-700">Name:</span>
                <span class="text-gray-800">{{ property.name }}</span>
            </div>
            <div>
                <span class="font-semibold text-gray-700">Type:</span>
                <span class="text-gray-800">{{ property.property_type }}</span>
            </div>
            <div>
                <span class="font-semibold text-gray-700">Location:</span>
                <span class="text-gray-800">{{ property.location }}</span>
            </div>
            <div>
                <span class="font-semibold text-gray-700">Price:</span>
                <span class="text-gray-800">₦{{ property.price|format_currency }}</span>
            </div>
            <div>
                <span class="font-semibold text-gray-700">Payment Cadence:</span>
                <span class="text-gray-800">{{ property.payment_cadence|replace('_', ' ')|title }}</span>
            </div>
            <div>
                <span class="font-semibold text-gray-700">Status:</span>
                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full 
                    {% if property.status == 'Available' %}bg-green-100 text-green-800
                    {% elif property.status == 'Sold' %}bg-blue-100 text-blue-800
                    {% elif property.status == 'Rented' %}bg-purple-100 text-purple-800
                    {% else %}bg-gray-100 text-gray-800{% endif %}">
                    {{ property.status }}
                </span>
            </div>
            <div>
                <span class="font-semibold text-gray-700">Payment Status:</span>
                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full 
                    {% if property.get_payment_status() == 'Up to Date' %}bg-green-100 text-green-800
                    {% elif property.get_payment_status().startswith('Overdue') %}bg-red-100 text-red-800
                    {% elif property.get_payment_status() == 'Never Paid' %}bg-yellow-100 text-yellow-800
                    {% else %}bg-gray-100 text-gray-800{% endif %}">
                    {{ property.get_payment_status() }}
                </span>
            </div>
            {% if property.get_payment_status().startswith('Overdue') or property.get_payment_status() == 'Never Paid' %}
            <div class="mt-2">
                <span class="font-semibold text-gray-700">Amount Due:</span>
                <span class="text-red-600 font-bold">₦{{ property.get_amount_due()|format_currency }}</span>
                <a href="{{ url_for('staff.create_payment') }}?property_id={{ property.id }}&amount={{ property.get_amount_due() }}" 
                   class="ml-2 bg-primary-600 hover:bg-primary-700 text-white text-xs py-1 px-2 rounded">
                    Record Payment
                </a>
            </div>
            {% endif %}
            <div>
                <span class="font-semibold text-gray-700">Description:</span>
                <p class="text-gray-800 mt-1">{{ property.description }}</p>
            </div>
        </div>
    </div>
    
    <!-- Property Actions -->
    <div class="bg-white shadow-md rounded-lg p-6">
        <h2 class="text-xl font-bold text-green-700 mb-4">Quick Actions</h2>
        <div class="grid grid-cols-1 gap-4">
            <a href="{{ url_for('staff.edit_property', id=property.id) }}" class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded text-center">
                Edit Property
            </a>
            <a href="#" class="bg-purple-600 hover:bg-purple-700 text-white font-bold py-2 px-4 rounded text-center">
                Record Payment
            </a>
            <a href="{{ url_for('staff.property_list') }}" class="bg-gray-600 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded text-center">
                Back to List
            </a>
        </div>
    </div>
</div>

<!-- Clients Section -->
<div class="bg-white shadow-md rounded-lg overflow-hidden mb-8">
    <div class="px-6 py-4 bg-gray-50 border-b border-gray-200 flex justify-between items-center">
        <h2 class="text-xl font-bold text-green-700">Assigned Clients</h2>
    </div>
    
    {% if clients %}
        <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
                <tr>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Client Name</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Email</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Phone</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
                {% for client in clients %}
                    <tr>
                        <td class="px-6 py-4 whitespace-nowrap">{{ client.full_name }}</td>
                        <td class="px-6 py-4 whitespace-nowrap">{{ client.email }}</td>
                        <td class="px-6 py-4 whitespace-nowrap">{{ client.phone }}</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                            <a href="{{ url_for('staff.client_detail', client_id=client.id) }}" class="text-blue-600 hover:text-blue-900">View</a>
                        </td>
                    </tr>
                {% endfor %}
            </tbody>
        </table>
    {% else %}
        <div class="px-6 py-4 text-center text-gray-500">
            No clients assigned to this property.
        </div>
    {% endif %}
</div>

<!-- Payments Section -->
<div class="bg-white shadow-md rounded-lg overflow-hidden mb-8">
    <div class="px-6 py-4 bg-gray-50 border-b border-gray-200 flex justify-between items-center">
        <h2 class="text-xl font-bold text-green-700">Payment History</h2>
    </div>
    
    {% if payments %}
        <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
                <tr>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Client</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Amount</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
                {% for payment in payments %}
                    <tr>
                        <td class="px-6 py-4 whitespace-nowrap">{{ payment.payment_date.strftime('%Y-%m-%d') }}</td>
                        <td class="px-6 py-4 whitespace-nowrap">{{ payment.client.full_name }}</td>
                        <td class="px-6 py-4 whitespace-nowrap">₦{{ payment.amount|format_currency }}</td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full 
                                {% if payment.status == 'Completed' %}bg-green-100 text-green-800
                                {% elif payment.status == 'Pending' %}bg-yellow-100 text-yellow-800
                                {% else %}bg-red-100 text-red-800{% endif %}">
                                {{ payment.status }}
                            </span>
                        </td>
                    </tr>
                {% endfor %}
            </tbody>
        </table>
    {% else %}
        <div class="px-6 py-4 text-center text-gray-500">
            No payment history for this property.
        </div>
    {% endif %}
</div>
{% endblock %}
