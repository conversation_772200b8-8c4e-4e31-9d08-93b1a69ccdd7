{% extends "layout.html" %}

{% block title %}Staff Dashboard - Green Peak Shelters Portal{% endblock %}
{% block header_title %}Staff Dashboard{% endblock %}
{% block header_subtitle %}Overview and Analytics{% endblock %}

{% block content %}
<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
    <!-- Client Stats -->
    <div class="bg-white shadow-md rounded-lg overflow-hidden">
        <div class="p-6">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-gray-500 text-sm">Total Clients</p>
                    <h3 class="text-3xl font-bold text-primary-700">{{ client_count }}</h3>
                </div>
                <div class="bg-primary-100 p-3 rounded-full">
                    <svg class="h-6 w-6 text-primary-700" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                    </svg>
                </div>
            </div>
            <div class="mt-4">
                <a href="{{ url_for('staff.client_list') }}" class="text-primary-600 hover:text-primary-800 text-sm font-medium">View all clients →</a>
            </div>
        </div>
    </div>
    
    <!-- Property Stats -->
    <div class="bg-white shadow-md rounded-lg overflow-hidden">
        <div class="p-6">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-gray-500 text-sm">Total Properties</p>
                    <h3 class="text-3xl font-bold text-secondary-700">{{ property_count }}</h3>
                </div>
                <div class="bg-secondary-100 p-3 rounded-full">
                    <svg class="h-6 w-6 text-secondary-700" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"></path>
                    </svg>
                </div>
            </div>
            <div class="mt-4">
                <a href="{{ url_for('staff.property_list') }}" class="text-secondary-600 hover:text-secondary-800 text-sm font-medium">View all properties →</a>
            </div>
        </div>
    </div>
    
    <!-- Service Stats -->
    <div class="bg-white shadow-md rounded-lg overflow-hidden">
        <div class="p-6">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-gray-500 text-sm">Total Services</p>
                    <h3 class="text-3xl font-bold text-purple-700">{{ service_count }}</h3>
                </div>
                <div class="bg-purple-100 p-3 rounded-full">
                    <svg class="h-6 w-6 text-purple-700" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 13.255A23.931 23.931 0 0112 15c-3.183 0-6.22-.62-9-1.745M16 6V4a2 2 0 00-2-2h-4a2 2 0 00-2 2v2m4 6h.01M5 20h14a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                    </svg>
                </div>
            </div>
            <div class="mt-4">
                <a href="{{ url_for('staff.service_list') }}" class="text-purple-600 hover:text-purple-800 text-sm font-medium">View all services →</a>
            </div>
        </div>
    </div>
    
    <!-- Payment Stats -->
    <div class="bg-white shadow-md rounded-lg overflow-hidden">
        <div class="p-6">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-gray-500 text-sm">Total Payments</p>
                    <h3 class="text-3xl font-bold text-amber-700">₦{{ total_payments }}</h3>
                </div>
                <div class="bg-amber-100 p-3 rounded-full">
                    <svg class="h-6 w-6 text-amber-700" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                </div>
            </div>
            <div class="mt-4">
                <a href="{{ url_for('staff.payment_list') }}" class="text-amber-600 hover:text-amber-800 text-sm font-medium">View all payments →</a>
            </div>
        </div>
    </div>
</div>

<!-- Overdue Payments Section -->
<div class="bg-white shadow-md rounded-lg overflow-hidden mb-8">
    <div class="px-6 py-4 bg-gray-50 border-b border-gray-200">
        <h2 class="text-xl font-bold text-red-700">Overdue Payments</h2>
    </div>
    <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
                <tr>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Property</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Client</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Amount Due</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
                {% if overdue_properties %}
                    {% for property in overdue_properties %}
                    <tr>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <a href="{{ url_for('staff.property_detail', id=property.id) }}" class="text-primary-600 hover:text-primary-800">
                                {{ property.name }}
                            </a>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            {% if property.clients %}
                                {% for client_property in property.client_properties %}
                                    <a href="{{ url_for('staff.client_detail', client_id=client_property.client.id) }}" class="text-primary-600 hover:text-primary-800">
                                        {{ client_property.client.full_name }}
                                    </a>
                                    {% if not loop.last %}, {% endif %}
                                {% endfor %}
                            {% else %}
                                <span class="text-gray-500">No client assigned</span>
                            {% endif %}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            {% set amount_owed = property.get_corrected_amount_due() %}
                            {% if amount_owed == 0 %}
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                                    Up to Date
                                </span>
                            {% else %}
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">
                                    Owes ₦{{ "{:,.2f}".format(amount_owed) }}
                                </span>
                            {% endif %}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">₦{{ property.get_corrected_amount_due()|format_currency }}</td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <a href="{{ url_for('staff.create_payment') }}?property_id={{ property.id }}&amount={{ property.get_corrected_amount_due() }}" class="text-primary-600 hover:text-primary-800">Record Payment</a>
                        </td>
                    </tr>
                    {% endfor %}
                {% else %}
                    <tr>
                        <td colspan="5" class="px-6 py-4 text-center text-gray-500">No overdue payments</td>
                    </tr>
                {% endif %}
            </tbody>
        </table>
    </div>
    {% if overdue_properties %}
        <div class="px-6 py-4 bg-gray-50 border-t border-gray-200 text-right">
            <a href="{{ url_for('staff.property_list') }}" class="text-primary-600 hover:text-primary-800 text-sm font-medium">View all properties →</a>
        </div>
    {% endif %}
</div>

<!-- Recent Activity -->
<div class="bg-white shadow-md rounded-lg overflow-hidden mb-8">
    <div class="px-6 py-4 bg-gray-50 border-b border-gray-200">
        <h2 class="text-xl font-bold text-primary-700">Recent Activity</h2>
    </div>
    <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
                <tr>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Client</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Activity</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
                {% if recent_activities %}
                    {% for activity in recent_activities %}
                    <tr>
                        <td class="px-6 py-4 whitespace-nowrap">{{ activity.date.strftime('%Y-%m-%d') }}</td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <a href="{{ url_for('staff.client_detail', client_id=activity.client.id) }}" class="text-primary-600 hover:text-primary-800">
                                {{ activity.client.full_name }}
                            </a>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">{{ activity.description }}</td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full 
                                {% if activity.status == 'Completed' %}bg-green-100 text-green-800
                                {% elif activity.status == 'Pending' %}bg-yellow-100 text-yellow-800
                                {% else %}bg-red-100 text-red-800{% endif %}">
                                {{ activity.status }}
                            </span>
                        </td>
                    </tr>
                    {% endfor %}
                {% else %}
                    <tr>
                        <td colspan="4" class="px-6 py-4 text-center text-gray-500">No recent activities</td>
                    </tr>
                {% endif %}
            </tbody>
        </table>
    </div>
</div>

<!-- Payment Analytics -->
<div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
    <div class="bg-white shadow-md rounded-lg overflow-hidden">
        <div class="px-6 py-4 bg-gray-50 border-b border-gray-200">
            <h2 class="text-xl font-bold text-primary-700">Monthly Payments (YTD)</h2>
        </div>
        <div class="p-6">
            <div class="h-64">
                <!-- Chart will be rendered here -->
                <canvas id="monthlyPaymentsChart"></canvas>
            </div>
        </div>
    </div>

    <div class="bg-white shadow-md rounded-lg overflow-hidden">
        <div class="px-6 py-4 bg-gray-50 border-b border-gray-200">
            <h2 class="text-xl font-bold text-primary-700">Payment Overview</h2>
        </div>
        <div class="p-6">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div class="bg-green-50 p-6 rounded-lg text-center">
                    <p class="text-green-700 text-sm font-medium">Total Amount Paid</p>
                    <h3 class="text-3xl font-bold text-green-700">₦{{ "{:,.2f}".format(total_amount_paid) }}</h3>
                    <div class="mt-2 text-xs text-green-600">
                        <div>Properties: ₦{{ "{:,.2f}".format(total_paid_properties) }}</div>
                        <div>Services: ₦{{ "{:,.2f}".format(total_paid_services) }}</div>
                    </div>
                </div>
                <div class="bg-red-50 p-6 rounded-lg text-center">
                    <p class="text-red-700 text-sm font-medium">Total Amount Owed</p>
                    <h3 class="text-3xl font-bold text-red-700">₦{{ "{:,.2f}".format(total_amount_owed) }}</h3>
                    <div class="mt-2 text-xs text-red-600">
                        <div>Properties: ₦{{ "{:,.2f}".format(total_owed_properties) }}</div>
                        <div>Services: ₦{{ "{:,.2f}".format(total_owed_services) }}</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Payment Breakdown Chart -->
<div class="bg-white shadow-md rounded-lg overflow-hidden mb-8">
    <div class="px-6 py-4 bg-gray-50 border-b border-gray-200">
        <h2 class="text-xl font-bold text-primary-700">Payment Breakdown</h2>
    </div>
    <div class="p-6">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
                <h3 class="text-lg font-semibold text-gray-700 mb-4">Amounts Paid vs Owed</h3>
                <div class="h-64">
                    <canvas id="paymentBreakdownChart"></canvas>
                </div>
            </div>
            <div>
                <h3 class="text-lg font-semibold text-gray-700 mb-4">Properties vs Services</h3>
                <div class="h-64">
                    <canvas id="categoryBreakdownChart"></canvas>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Legacy Payment Status -->
<div class="bg-white shadow-md rounded-lg overflow-hidden mb-8">
    <div class="px-6 py-4 bg-gray-50 border-b border-gray-200">
        <h2 class="text-xl font-bold text-primary-700">Legacy Payment Status</h2>
    </div>
    <div class="p-6">
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div class="bg-blue-50 p-4 rounded-lg text-center">
                <p class="text-blue-700 text-sm">Properties with Debt</p>
                <h3 class="text-2xl font-bold text-blue-700">{{ overdue_properties|length }}</h3>
            </div>
            <div class="bg-yellow-50 p-4 rounded-lg text-center">
                <p class="text-yellow-700 text-sm">Total Properties</p>
                <h3 class="text-2xl font-bold text-yellow-700">{{ property_count }}</h3>
            </div>
            <div class="bg-purple-50 p-4 rounded-lg text-center">
                <p class="text-purple-700 text-sm">Total Services</p>
                <h3 class="text-2xl font-bold text-purple-700">{{ service_count }}</h3>
            </div>
        </div>
        <div class="mt-6">
            <div class="h-64">
                <canvas id="paymentStatusChart"></canvas>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Monthly Payments Chart
        const monthlyCtx = document.getElementById('monthlyPaymentsChart').getContext('2d');
        const monthlyChart = new Chart(monthlyCtx, {
            type: 'bar',
            data: {
                labels: {{ months|tojson }},
                datasets: [{
                    label: 'Monthly Payments (₦)',
                    data: {{ monthly_payments|tojson }},
                    backgroundColor: 'rgba(22, 163, 74, 0.6)',
                    borderColor: 'rgba(22, 163, 74, 1)',
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            callback: function(value) {
                                return '₦' + value;
                            }
                        }
                    }
                }
            }
        });
        
        // Payment Breakdown Chart (Paid vs Owed)
        const breakdownCtx = document.getElementById('paymentBreakdownChart').getContext('2d');
        const breakdownChart = new Chart(breakdownCtx, {
            type: 'doughnut',
            data: {
                labels: ['Amount Paid', 'Amount Owed'],
                datasets: [{
                    data: [
                        {{ total_amount_paid }},
                        {{ total_amount_owed }}
                    ],
                    backgroundColor: [
                        'rgba(22, 163, 74, 0.6)',
                        'rgba(220, 38, 38, 0.6)'
                    ],
                    borderColor: [
                        'rgba(22, 163, 74, 1)',
                        'rgba(220, 38, 38, 1)'
                    ],
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                return context.label + ': ₦' + context.parsed.toLocaleString();
                            }
                        }
                    }
                }
            }
        });

        // Category Breakdown Chart (Properties vs Services)
        const categoryCtx = document.getElementById('categoryBreakdownChart').getContext('2d');
        const categoryChart = new Chart(categoryCtx, {
            type: 'bar',
            data: {
                labels: ['Properties', 'Services'],
                datasets: [{
                    label: 'Amount Paid (₦)',
                    data: [{{ total_paid_properties }}, {{ total_paid_services }}],
                    backgroundColor: 'rgba(22, 163, 74, 0.6)',
                    borderColor: 'rgba(22, 163, 74, 1)',
                    borderWidth: 1
                }, {
                    label: 'Amount Owed (₦)',
                    data: [{{ total_owed_properties }}, {{ total_owed_services }}],
                    backgroundColor: 'rgba(220, 38, 38, 0.6)',
                    borderColor: 'rgba(220, 38, 38, 1)',
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            callback: function(value) {
                                return '₦' + value.toLocaleString();
                            }
                        }
                    }
                },
                plugins: {
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                return context.dataset.label + ': ₦' + context.parsed.y.toLocaleString();
                            }
                        }
                    }
                }
            }
        });

        // Legacy Payment Status Chart (if still needed)
        const statusCtx = document.getElementById('paymentStatusChart');
        if (statusCtx) {
            const statusChart = new Chart(statusCtx.getContext('2d'), {
                type: 'doughnut',
                data: {
                    labels: ['Properties with Debt', 'Total Properties', 'Total Services'],
                    datasets: [{
                        data: [
                            {{ overdue_properties|length }},
                            {{ property_count }},
                            {{ service_count }}
                        ],
                        backgroundColor: [
                            'rgba(59, 130, 246, 0.6)',
                            'rgba(234, 179, 8, 0.6)',
                            'rgba(147, 51, 234, 0.6)'
                        ],
                        borderColor: [
                            'rgba(59, 130, 246, 1)',
                            'rgba(234, 179, 8, 1)',
                            'rgba(147, 51, 234, 1)'
                        ],
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                }
            });
        }
    });
</script>
{% endblock %}
