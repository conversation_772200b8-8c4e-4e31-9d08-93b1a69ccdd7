{% extends "layout.html" %}

{% block title %}Payments - Green Peak Shelters Portal{% endblock %}
{% block header_title %}Payments{% endblock %}
{% block header_subtitle %}Manage and track all client payments{% endblock %}

{% block content %}
<div class="bg-white shadow-md rounded-lg p-6">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold text-gray-800">All Payments</h2>
        <a href="{{ url_for('staff.create_payment') }}" class="bg-primary-600 hover:bg-primary-700 text-white font-medium py-2 px-4 rounded focus:outline-none focus:shadow-outline">
            Record New Payment
        </a>
    </div>
    
    <div class="overflow-x-auto">
        <table class="min-w-full bg-white">
            <thead>
                <tr>
                    <th class="py-3 px-4 bg-gray-100 text-left text-xs font-medium text-gray-600 uppercase tracking-wider">ID</th>
                    <th class="py-3 px-4 bg-gray-100 text-left text-xs font-medium text-gray-600 uppercase tracking-wider">Client</th>
                    <th class="py-3 px-4 bg-gray-100 text-left text-xs font-medium text-gray-600 uppercase tracking-wider">Amount</th>
                    <th class="py-3 px-4 bg-gray-100 text-left text-xs font-medium text-gray-600 uppercase tracking-wider">Date</th>
                    <th class="py-3 px-4 bg-gray-100 text-left text-xs font-medium text-gray-600 uppercase tracking-wider">Status</th>
                    <th class="py-3 px-4 bg-gray-100 text-left text-xs font-medium text-gray-600 uppercase tracking-wider">Type</th>
                    <th class="py-3 px-4 bg-gray-100 text-left text-xs font-medium text-gray-600 uppercase tracking-wider">Actions</th>
                </tr>
            </thead>
            <tbody class="divide-y divide-gray-200">
                {% for payment in payments %}
                <tr>
                    <td class="py-4 px-4 text-sm text-gray-900">{{ payment.id }}</td>
                    <td class="py-4 px-4 text-sm text-gray-900">{{ payment.client.firstname }} {{ payment.client.lastname }}</td>
                    <td class="py-4 px-4 text-sm text-gray-900">₦{{ "{:,.2f}".format(payment.amount) }}</td>
                    <td class="py-4 px-4 text-sm text-gray-900">{{ payment.payment_date.strftime('%Y-%m-%d') }}</td>
                    <td class="py-4 px-4 text-sm">
                        {% if payment.status == 'Completed' %}
                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                            {{ payment.status }}
                        </span>
                        {% elif payment.status == 'Pending' %}
                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800">
                            {{ payment.status }}
                        </span>
                        {% elif payment.status == 'Partial' %}
                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 text-blue-800">
                            {{ payment.status }}
                        </span>
                        {% else %}
                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">
                            {{ payment.status }}
                        </span>
                        {% endif %}
                    </td>
                    <td class="py-4 px-4 text-sm text-gray-900">{{ payment.payment_type }}</td>
                    <td class="py-4 px-4 text-sm text-gray-900">
                        <a href="#" class="text-primary-600 hover:text-primary-800 mr-3">View</a>
                        <a href="#" class="text-primary-600 hover:text-primary-800">Receipt</a>
                    </td>
                </tr>
                {% endfor %}
                
                {% if not payments %}
                <tr>
                    <td colspan="7" class="py-4 px-4 text-sm text-gray-500 text-center">No payments found.</td>
                </tr>
                {% endif %}
            </tbody>
        </table>
    </div>
</div>
{% endblock %}
