{% extends "layout.html" %}

{% block title %}Record Payment - Green Peak Shelters Portal{% endblock %}
{% block header_title %}Record Payment{% endblock %}
{% block header_subtitle %}Record a new payment for a client{% endblock %}

{% block content %}
<div class="bg-white shadow-md rounded-lg p-6 max-w-2xl mx-auto">
    <h1 class="text-2xl font-bold text-primary-700 mb-6">Record New Payment</h1>
    
    {% with messages = get_flashed_messages(with_categories=true) %}
        {% if messages %}
            {% for category, message in messages %}
                <div class="p-4 mb-4 {% if category == 'error' %}bg-red-100 text-red-700{% else %}bg-green-100 text-green-700{% endif %} rounded">
                    {{ message }}
                </div>
            {% endfor %}
        {% endif %}
    {% endwith %}
    
    <form method="POST">
        <div class="mb-4">
            <label for="client_id" class="block text-gray-700 font-medium mb-2">Client</label>
            <select id="client_id" name="client_id" required
                class="w-full px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500">
                <option value="">Select a client</option>
                {% for client in clients %}
                    <option value="{{ client.id }}" {% if selected_property and client in selected_property.clients %}selected{% endif %}>
                        {{ client.full_name }} ({{ client.email }})
                    </option>
                {% endfor %}
            </select>
        </div>
        
        <div class="mb-4">
            <label class="block text-gray-700 font-medium mb-2">Payment For</label>
            <div class="flex items-center space-x-4">
                <label class="inline-flex items-center">
                    <input type="radio" name="item_type" value="property" class="form-radio" {% if selected_property %}checked{% endif %} required>
                    <span class="ml-2">Property</span>
                </label>
                <label class="inline-flex items-center">
                    <input type="radio" name="item_type" value="service" class="form-radio" {% if not selected_property %}checked{% endif %}>
                    <span class="ml-2">Service</span>
                </label>
            </div>
        </div>
        
        <div id="property-select" class="mb-4 {% if not selected_property %}hidden{% endif %}">
            <label for="property_id" class="block text-gray-700 font-medium mb-2">Property</label>
            <select id="property_id" name="item_id" 
                class="w-full px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500">
                <option value="">Select a property</option>
                {% for property in properties %}
                    <option value="{{ property.id }}" {% if selected_property and property.id == selected_property.id %}selected{% endif %}>
                        {{ property.name }} (₦{{ property.price|format_currency }})
                    </option>
                {% endfor %}
            </select>
        </div>
        
        <div id="service-select" class="mb-4 {% if selected_property %}hidden{% endif %}">
            <label for="service_id" class="block text-gray-700 font-medium mb-2">Service</label>
            <select id="service_id" name="item_id" 
                class="w-full px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500">
                <option value="">Select a service</option>
                {% for service in services %}
                    <option value="{{ service.id }}">
                        {{ service.name }} (₦{{ service.price|format_currency }})
                    </option>
                {% endfor %}
            </select>
        </div>
        
        <div class="mb-4">
            <label for="amount" class="block text-gray-700 font-medium mb-2">Amount (₦)</label>
            <input type="number" id="amount" name="amount" value="{{ amount }}" required min="0" step="0.01"
                class="w-full px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500">
        </div>
        
        <div class="mb-4">
            <label for="payment_date" class="block text-gray-700 font-medium mb-2">Payment Date</label>
            <input type="date" id="payment_date" name="payment_date" value="{{ today }}" required
                class="w-full px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500">
        </div>
        
        <div class="mb-4">
            <label for="status" class="block text-gray-700 font-medium mb-2">Status</label>
            <select id="status" name="status" required
                class="w-full px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500">
                <option value="Completed" selected>Completed</option>
                <option value="Partial">Partial</option>
                <option value="Pending">Pending</option>
                <option value="Failed">Failed</option>
            </select>
        </div>
        
        <div class="flex justify-between mt-6">
            <a href="{{ url_for('staff.dashboard') }}" class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded">
                Cancel
            </a>
            <button type="submit" class="bg-primary-600 hover:bg-primary-700 text-white font-bold py-2 px-4 rounded">
                Record Payment
            </button>
        </div>
    </form>
</div>

<script>
    // Toggle between property and service selection
    document.addEventListener('DOMContentLoaded', function() {
        const propertyRadio = document.querySelector('input[name="item_type"][value="property"]');
        const serviceRadio = document.querySelector('input[name="item_type"][value="service"]');
        const propertySelect = document.getElementById('property-select');
        const serviceSelect = document.getElementById('service-select');
        const propertyIdSelect = document.getElementById('property_id');
        const serviceIdSelect = document.getElementById('service_id');
        
        propertyRadio.addEventListener('change', function() {
            if (this.checked) {
                propertySelect.classList.remove('hidden');
                serviceSelect.classList.add('hidden');
                propertyIdSelect.setAttribute('required', '');
                serviceIdSelect.removeAttribute('required');
            }
        });
        
        serviceRadio.addEventListener('change', function() {
            if (this.checked) {
                serviceSelect.classList.remove('hidden');
                propertySelect.classList.add('hidden');
                serviceIdSelect.setAttribute('required', '');
                propertyIdSelect.removeAttribute('required');
            }
        });
    });
</script>
{% endblock %}
