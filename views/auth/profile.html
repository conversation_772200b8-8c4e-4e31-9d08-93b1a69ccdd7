{% extends "layout.html" %}

{% block title %}User Profile - Green Peak Shelters Portal{% endblock %}
{% block header_title %}User Profile{% endblock %}
{% block header_subtitle %}Manage your account information{% endblock %}

{% block content %}
<div class="bg-white shadow-md rounded-lg p-6 max-w-2xl mx-auto">
    <h2 class="text-2xl font-bold text-gray-800 mb-6">Profile Information</h2>
    
    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
            <p class="text-gray-600 text-sm">First Name</p>
            <p class="text-gray-900 font-medium">{{ user.firstname }}</p>
        </div>
        <div>
            <p class="text-gray-600 text-sm">Last Name</p>
            <p class="text-gray-900 font-medium">{{ user.lastname }}</p>
        </div>
        <div>
            <p class="text-gray-600 text-sm">Email</p>
            <p class="text-gray-900 font-medium">{{ user.email }}</p>
        </div>
        <div>
            <p class="text-gray-600 text-sm">Role</p>
            <p class="text-gray-900 font-medium">{{ user.role|title }}</p>
        </div>
        <div class="md:col-span-2">
            <p class="text-gray-600 text-sm">Password Security</p>
            <div class="flex items-center space-x-2">
                {% if password_info.type == 'bcrypt' %}
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                        Secure (bcrypt)
                    </span>
                    {% if password_info.strength == 'Strong' %}
                        <span class="text-green-600 text-sm">{{ password_info.strength }}</span>
                    {% elif password_info.strength == 'Good' %}
                        <span class="text-yellow-600 text-sm">{{ password_info.strength }}</span>
                    {% else %}
                        <span class="text-red-600 text-sm">{{ password_info.strength }}</span>
                    {% endif %}
                {% elif password_info.type == 'legacy' %}
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                        Legacy (will upgrade on next login)
                    </span>
                {% else %}
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                        No password set
                    </span>
                {% endif %}
            </div>
        </div>
    </div>
    
    <div class="mt-8 flex justify-end space-x-3">
        <a href="{{ url_for('auth.sessions') }}" class="bg-gray-600 hover:bg-gray-700 text-white font-medium py-2 px-4 rounded focus:outline-none focus:shadow-outline">
            Manage Sessions
        </a>
        <a href="{{ url_for('auth.change_password') }}" class="bg-primary-600 hover:bg-primary-700 text-white font-medium py-2 px-4 rounded focus:outline-none focus:shadow-outline">
            Change Password
        </a>
    </div>
</div>
{% endblock %}