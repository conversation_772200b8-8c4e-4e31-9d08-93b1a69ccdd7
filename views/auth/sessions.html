{% extends "base.html" %}

{% block title %}Active Sessions - Green Peak Shelters{% endblock %}

{% block content %}
<div class="max-w-4xl mx-auto">
    <div class="flex justify-between items-center mb-6">
        <h1 class="text-2xl font-bold text-primary-700">Active Sessions</h1>
        <div class="space-x-2">
            <a href="{{ url_for('auth.profile') }}" 
               class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-md text-sm font-medium">
                Back to Profile
            </a>
            <form method="POST" action="{{ url_for('auth.logout_all') }}" class="inline">
                <button type="submit" 
                        onclick="return confirm('Are you sure you want to logout from all devices?')"
                        class="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-md text-sm font-medium">
                    Logout All Devices
                </button>
            </form>
        </div>
    </div>

    <div class="bg-white shadow-md rounded-lg overflow-hidden">
        <div class="px-6 py-4 bg-gray-50 border-b">
            <h2 class="text-lg font-medium text-gray-900">Session Management</h2>
            <p class="text-sm text-gray-600 mt-1">
                Manage your active sessions across different devices and browsers. 
                You can revoke access from devices you no longer use.
            </p>
        </div>

        {% if sessions %}
            <div class="divide-y divide-gray-200">
                {% for session in sessions %}
                    <div class="px-6 py-4 {% if session.id == current_session_id %}bg-blue-50{% endif %}">
                        <div class="flex items-center justify-between">
                            <div class="flex-1">
                                <div class="flex items-center space-x-3">
                                    <div class="flex-shrink-0">
                                        {% if session.id == current_session_id %}
                                            <div class="w-3 h-3 bg-green-500 rounded-full"></div>
                                        {% else %}
                                            <div class="w-3 h-3 bg-gray-400 rounded-full"></div>
                                        {% endif %}
                                    </div>
                                    <div class="flex-1 min-w-0">
                                        <div class="flex items-center space-x-2">
                                            <p class="text-sm font-medium text-gray-900">
                                                {% if session.id == current_session_id %}
                                                    Current Session
                                                {% else %}
                                                    Session {{ session.id[:8] }}...
                                                {% endif %}
                                            </p>
                                            {% if session.id == current_session_id %}
                                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                                    Current
                                                </span>
                                            {% endif %}
                                        </div>
                                        
                                        <div class="mt-1 space-y-1">
                                            {% if session.ip_address %}
                                                <p class="text-sm text-gray-500">
                                                    <span class="font-medium">IP:</span> {{ session.ip_address }}
                                                </p>
                                            {% endif %}
                                            
                                            {% if session.user_agent %}
                                                <p class="text-sm text-gray-500">
                                                    <span class="font-medium">Device:</span> 
                                                    {% set ua = session.user_agent %}
                                                    {% if 'Chrome' in ua %}
                                                        Chrome Browser
                                                    {% elif 'Firefox' in ua %}
                                                        Firefox Browser
                                                    {% elif 'Safari' in ua %}
                                                        Safari Browser
                                                    {% elif 'Edge' in ua %}
                                                        Edge Browser
                                                    {% else %}
                                                        {{ ua[:50] }}{% if ua|length > 50 %}...{% endif %}
                                                    {% endif %}
                                                </p>
                                            {% endif %}
                                            
                                            <div class="flex space-x-4 text-sm text-gray-500">
                                                <span>
                                                    <span class="font-medium">Created:</span> 
                                                    {{ session.created_at.strftime('%Y-%m-%d %H:%M') }}
                                                </span>
                                                <span>
                                                    <span class="font-medium">Last Active:</span> 
                                                    {{ session.updated_at.strftime('%Y-%m-%d %H:%M') }}
                                                </span>
                                                <span>
                                                    <span class="font-medium">Expires:</span> 
                                                    {{ session.expires_at.strftime('%Y-%m-%d %H:%M') }}
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="flex-shrink-0 ml-4">
                                {% if session.id != current_session_id %}
                                    <form method="POST" action="{{ url_for('auth.revoke_session', session_id=session.id) }}" class="inline">
                                        <button type="submit" 
                                                onclick="return confirm('Are you sure you want to revoke this session?')"
                                                class="bg-red-600 hover:bg-red-700 text-white px-3 py-1 rounded text-sm font-medium">
                                            Revoke
                                        </button>
                                    </form>
                                {% else %}
                                    <a href="{{ url_for('auth.logout') }}" 
                                       onclick="return confirm('Are you sure you want to logout?')"
                                       class="bg-gray-600 hover:bg-gray-700 text-white px-3 py-1 rounded text-sm font-medium">
                                        Logout
                                    </a>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                {% endfor %}
            </div>
        {% else %}
            <div class="px-6 py-8 text-center">
                <div class="text-gray-500">
                    <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                    </svg>
                    <h3 class="mt-2 text-sm font-medium text-gray-900">No active sessions</h3>
                    <p class="mt-1 text-sm text-gray-500">You don't have any active sessions.</p>
                </div>
            </div>
        {% endif %}
    </div>

    <div class="mt-6 bg-blue-50 border border-blue-200 rounded-lg p-4">
        <div class="flex">
            <div class="flex-shrink-0">
                <svg class="h-5 w-5 text-blue-400" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
                </svg>
            </div>
            <div class="ml-3">
                <h3 class="text-sm font-medium text-blue-800">Security Information</h3>
                <div class="mt-2 text-sm text-blue-700">
                    <ul class="list-disc list-inside space-y-1">
                        <li>Sessions automatically expire after a period of inactivity</li>
                        <li>If you notice any suspicious sessions, revoke them immediately</li>
                        <li>Use "Logout All Devices" if you suspect unauthorized access</li>
                        <li>Always logout from shared or public computers</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
