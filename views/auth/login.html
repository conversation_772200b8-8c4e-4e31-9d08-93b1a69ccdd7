<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login - Green Peak Shelters Portal</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Inter', sans-serif;
            background-color: #F9FAFB;
        }
        .login-container {
            min-height: 100vh;
            display: flex;
        }
        .login-image {
            background-image: url('/assets/imgs/bg.png');
            background-size: cover;
            background-position: center;
        }
        .login-overlay {
            background-color: rgba(34, 79, 52, 0.8); /* <PERSON> with opacity */
        }
        .form-input:focus {
            border-color: #224F34;
            box-shadow: 0 0 0 3px rgba(34, 79, 52, 0.2);
        }
        .btn-primary {
            background-color: #224F34;
        }
        .btn-primary:hover {
            background-color: #1a3d28;
        }
    </style>
</head>
<body>
    <div class="login-container">
        <!-- Left side - Image with overlay -->
        <div class="hidden md:flex md:w-1/2 login-image relative">
            <div class="login-overlay absolute inset-0 flex flex-col justify-center items-center text-white p-12">
                <img src="/assets/imgs/logo_round_trans.png" alt="Green Peak Shelters Logo" class="h-20 mb-8">
                <h1 class="text-4xl font-bold mb-6">Welcome to Green Peak Shelters</h1>
                <p class="text-xl mb-8 text-center">Your trusted partner in real estate management and services</p>
                <div class="mt-auto">
                    <p class="text-sm opacity-80">&copy; 2023 Green Peak Shelters. All rights reserved.</p>
                </div>
            </div>
        </div>
        
        <!-- Right side - Login form -->
        <div class="w-full md:w-1/2 flex items-center justify-center p-8">
            <div class="w-full max-w-md">
                <!-- Mobile logo (visible only on mobile) -->
                <div class="md:hidden flex justify-center mb-8">
                    <img src="/assets/imgs/GPLogo.png" alt="Green Peak Shelters Logo" class="h-16">
                </div>
                
                <h2 class="text-3xl font-bold text-gray-800 mb-2">Sign in</h2>
                <p class="text-gray-600 mb-8">Sign in to access your account</p>
                
                {% if get_flashed_messages(category_filter=["error"]) %}
                <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-4" role="alert">
                    {% for message in get_flashed_messages(category_filter=["error"]) %}
                    <p>{{ message }}</p>
                    {% endfor %}
                </div>
                {% endif %}
                
                {% if get_flashed_messages(category_filter=["success"]) %}
                <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded relative mb-4" role="alert">
                    {% for message in get_flashed_messages(category_filter=["success"]) %}
                    <p>{{ message }}</p>
                    {% endfor %}
                </div>
                {% endif %}
                
                <form method="POST" class="space-y-6">
                    <div>
                        <label for="email" class="block text-sm font-medium text-gray-700 mb-1">Email Address</label>
                        <input type="email" id="email" name="email" value="{{ email or '' }}" required 
                               class="form-input w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none">
                    </div>
                    
                    <div>
                        <div class="flex items-center justify-between mb-1">
                            <label for="password" class="block text-sm font-medium text-gray-700">Password</label>
                            <a href="{{ url_for('auth.forgot_password') }}" class="text-sm text-gray-600 hover:text-gray-900">Forgot password?</a>
                        </div>
                        <input type="password" id="password" name="password" required 
                               class="form-input w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none">
                    </div>
                    
                    <div class="flex items-center">
                        <input type="checkbox" id="remember" name="remember" class="h-4 w-4 text-green-600 border-gray-300 rounded">
                        <label for="remember" class="ml-2 block text-sm text-gray-700">Remember me</label>
                    </div>
                    
                    <div>
                        <button type="submit" 
                                class="btn-primary w-full flex justify-center py-3 px-4 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500">
                            Sign in
                        </button>
                    </div>
                    
                    <input type="hidden" name="next" value="{{ request.args.get('next', '') }}"/>
                </form>
                
                <div class="mt-8 text-center text-sm text-gray-600">
                    <p>Don't have an account? <a href="#" class="font-medium text-green-600 hover:text-green-500">Contact administrator</a></p>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
