{% extends "layout.html" %}

{% block title %}Change Password - Green Peak Shelters Portal{% endblock %}
{% block header_title %}Change Password{% endblock %}
{% block header_subtitle %}Update your account security{% endblock %}

{% block content %}
<div class="bg-white shadow-md rounded-lg p-6 max-w-md mx-auto">
    <h2 class="text-2xl font-bold text-gray-800 mb-6">Change Your Password</h2>
    
    <form method="POST" action="{{ url_for('auth.change_password') }}">
        <div class="mb-4">
            <label for="current_password" class="block text-gray-700 text-sm font-bold mb-2">Current Password</label>
            <input type="password" name="current_password" id="current_password" required 
                   class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline">
        </div>
        
        <div class="mb-4">
            <label for="new_password" class="block text-gray-700 text-sm font-bold mb-2">New Password</label>
            <input type="password" name="new_password" id="new_password" required
                   class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                   onkeyup="checkPasswordStrength()">

            <!-- Password strength indicator -->
            <div id="password-strength" class="mt-2 hidden">
                <div class="flex items-center space-x-2">
                    <div class="flex-1 bg-gray-200 rounded-full h-2">
                        <div id="strength-bar" class="h-2 rounded-full transition-all duration-300"></div>
                    </div>
                    <span id="strength-text" class="text-sm font-medium"></span>
                </div>
                <div id="password-feedback" class="mt-1 text-xs text-gray-600"></div>
            </div>
        </div>
        
        <div class="mb-6">
            <label for="confirm_password" class="block text-gray-700 text-sm font-bold mb-2">Confirm New Password</label>
            <input type="password" name="confirm_password" id="confirm_password" required 
                   class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline">
        </div>
        
        <div class="flex items-center justify-between">
            <a href="{{ url_for('auth.profile') }}" class="text-primary-600 hover:text-primary-800">
                Back to Profile
            </a>
            <button type="submit" class="bg-primary-600 hover:bg-primary-700 text-white font-medium py-2 px-4 rounded focus:outline-none focus:shadow-outline">
                Update Password
            </button>
        </div>
    </form>
</div>

<script>
let strengthTimeout;

function checkPasswordStrength() {
    const password = document.getElementById('new_password').value;
    const strengthDiv = document.getElementById('password-strength');
    const strengthBar = document.getElementById('strength-bar');
    const strengthText = document.getElementById('strength-text');
    const feedbackDiv = document.getElementById('password-feedback');

    // Clear previous timeout
    if (strengthTimeout) {
        clearTimeout(strengthTimeout);
    }

    // Don't check empty passwords
    if (!password) {
        strengthDiv.classList.add('hidden');
        return;
    }

    // Show strength indicator
    strengthDiv.classList.remove('hidden');

    // Debounce API calls
    strengthTimeout = setTimeout(() => {
        fetch('{{ url_for("auth.check_password_strength") }}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ password: password })
        })
        .then(response => response.json())
        .then(data => {
            const strength = data.strength;
            const score = strength.score;
            const level = strength.level;

            // Update strength bar
            const percentage = Math.min((score / 9) * 100, 100);
            strengthBar.style.width = percentage + '%';

            // Update colors based on strength
            if (level === 'Very Weak') {
                strengthBar.className = 'h-2 rounded-full transition-all duration-300 bg-red-500';
                strengthText.className = 'text-sm font-medium text-red-600';
            } else if (level === 'Weak') {
                strengthBar.className = 'h-2 rounded-full transition-all duration-300 bg-orange-500';
                strengthText.className = 'text-sm font-medium text-orange-600';
            } else if (level === 'Fair') {
                strengthBar.className = 'h-2 rounded-full transition-all duration-300 bg-yellow-500';
                strengthText.className = 'text-sm font-medium text-yellow-600';
            } else if (level === 'Good') {
                strengthBar.className = 'h-2 rounded-full transition-all duration-300 bg-blue-500';
                strengthText.className = 'text-sm font-medium text-blue-600';
            } else {
                strengthBar.className = 'h-2 rounded-full transition-all duration-300 bg-green-500';
                strengthText.className = 'text-sm font-medium text-green-600';
            }

            strengthText.textContent = level;

            // Update feedback
            if (data.errors && data.errors.length > 0) {
                feedbackDiv.innerHTML = data.errors.map(error =>
                    `<div class="text-red-600">• ${error}</div>`
                ).join('');
            } else {
                feedbackDiv.innerHTML = strength.feedback.map(feedback =>
                    `<div class="text-gray-600">• ${feedback}</div>`
                ).join('');
            }
        })
        .catch(error => {
            console.error('Error checking password strength:', error);
        });
    }, 300); // 300ms debounce
}
</script>

{% endblock %}