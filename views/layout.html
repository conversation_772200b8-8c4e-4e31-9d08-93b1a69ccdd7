<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}Green Peak Shelters Portal{% endblock %}</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="/assets/css/styles.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    {% block styles %}{% endblock %}
    <style>
        body {
            font-family: 'Inter', sans-serif;
            background-color: #F9FAFB;
            color: #374151;
        }
        #sidebar {
            background-color: #224F34;
        }
        .chart-container {
            position: relative;
            width: 100%;
            max-width: 800px;
            margin-left: auto;
            margin-right: auto;
            height: 300px;
            max-height: 400px;
        }
        @media (min-width: 768px) {
            .chart-container {
                height: 350px;
            }
        }
        @media (min-width: 1024px) {
            .chart-container {
                height: 400px;
            }
        }
        .text-xxs {
            font-size: 0.65rem;
            line-height: 0.8rem;
        }
        .custom-scrollbar::-webkit-scrollbar {
            width: 8px;
        }
        .custom-scrollbar::-webkit-scrollbar-track {
            background: #E5E7EB;
            border-radius: 10px;
        }
        .custom-scrollbar::-webkit-scrollbar-thumb {
            background: #9CA3AF;
            border-radius: 10px;
        }
        .custom-scrollbar::-webkit-scrollbar-thumb:hover {
            background: #6B7280;
        }
        .sidebar-icon-active {
            background-color: rgba(150, 200, 76, 0.2);
            color: #96C84C;
        }
    </style>
</head>
<body class="flex flex-col lg:flex-row min-h-screen">
    <!-- Top Header Bar for Mobile/Tablet -->
    <header class="lg:hidden flex items-center justify-between p-4 bg-white shadow-md z-10">
        <button id="mobile-menu-toggle" class="text-gray-700 text-2xl p-2 rounded-md hover:bg-gray-200">☰</button>
        <div class="text-xl font-semibold text-gray-800">Green Peak Shelters</div>
        <div class="flex items-center space-x-4">
            <span class="text-gray-700 text-xl">🔍</span>
            <span class="text-gray-700 text-xl">🔔</span>
            {% if user %}
            <div class="w-8 h-8 rounded-full bg-gray-300 flex items-center justify-center text-gray-700 text-sm">
                {{ user.firstname[0] }}{{ user.lastname[0] }}
            </div>
            {% endif %}
        </div>
    </header>

    <!-- Sidebar Navigation -->
    <nav id="sidebar" class="fixed inset-y-0 left-0 w-20 bg-[#224F34] text-gray-200 p-4 flex flex-col items-center shadow-lg transform -translate-x-full lg:translate-x-0 transition-transform duration-300 ease-in-out z-20">
        <div class="mb-8">
            <img src="/assets/imgs/logo_round_trans.png" alt="GP Logo" class="h-10 w-auto" onerror="this.onerror=null; this.src='https://placehold.co/40x40/224F34/FFFFFF?text=GP';">
        </div>
        <ul class="space-y-6 w-full">
            {% if user %}
                {% if user.role == 'staff' %}
                <li>
                    <a href="{{ url_for('staff.dashboard') }}" class="flex flex-col items-center p-2 rounded-xl hover:bg-green-700 group {% if request.endpoint == 'staff.dashboard' %}sidebar-icon-active{% endif %}">
                        <span class="text-2xl">🏠</span>
                        <span class="text-xs mt-1">Home</span>
                    </a>
                </li>
                <li>
                    <a href="{{ url_for('staff.client_list') }}" class="flex flex-col items-center p-2 rounded-xl hover:bg-green-700 group {% if request.endpoint == 'staff.client_list' %}sidebar-icon-active{% endif %}">
                        <span class="text-2xl">👥</span>
                        <span class="text-xs mt-1">Clients</span>
                    </a>
                </li>
                <li>
                    <a href="{{ url_for('staff.property_list') }}" class="flex flex-col items-center p-2 rounded-xl hover:bg-green-700 group {% if request.endpoint == 'staff.property_list' %}sidebar-icon-active{% endif %}">
                        <span class="text-2xl">🏢</span>
                        <span class="text-xs mt-1">Properties</span>
                    </a>
                </li>
                <li>
                    <a href="{{ url_for('staff.service_list') }}" class="flex flex-col items-center p-2 rounded-xl hover:bg-green-700 group {% if request.endpoint == 'staff.service_list' %}sidebar-icon-active{% endif %}">
                        <span class="text-2xl">🛠️</span>
                        <span class="text-xs mt-1">Services</span>
                    </a>
                </li>
                <li>
                    <a href="{{ url_for('staff.payment_list') }}" class="flex flex-col items-center p-2 rounded-xl hover:bg-green-700 group {% if request.endpoint == 'staff.payment_list' %}sidebar-icon-active{% endif %}">
                        <span class="text-2xl">💰</span>
                        <span class="text-xs mt-1">Payments</span>
                    </a>
                </li>
                <li>
                    <a href="{{ url_for('staff.report_list') }}" class="flex flex-col items-center p-2 rounded-xl hover:bg-green-700 group {% if request.endpoint == 'staff.report_list' %}sidebar-icon-active{% endif %}">
                        <span class="text-2xl">📊</span>
                        <span class="text-xs mt-1">Reports</span>
                    </a>
                </li>
                {% elif user.role == 'super_user' %}
                <li>
                    <a href="{{ url_for('admin.dashboard') }}" class="flex flex-col items-center p-2 rounded-xl hover:bg-green-700 group {% if request.endpoint == 'admin.dashboard' %}sidebar-icon-active{% endif %}">
                        <span class="text-2xl">🏠</span>
                        <span class="text-xs mt-1">Home</span>
                    </a>
                </li>
                <li>
                    <a href="{{ url_for('admin.staff_list') }}" class="flex flex-col items-center p-2 rounded-xl hover:bg-green-700 group {% if request.endpoint == 'admin.staff_list' %}sidebar-icon-active{% endif %}">
                        <span class="text-2xl">👨‍💼</span>
                        <span class="text-xs mt-1">Staff</span>
                    </a>
                </li>
                <li>
                    <a href="{{ url_for('admin.client_list') }}" class="flex flex-col items-center p-2 rounded-xl hover:bg-green-700 group {% if request.endpoint == 'admin.client_list' %}sidebar-icon-active{% endif %}">
                        <span class="text-2xl">👥</span>
                        <span class="text-xs mt-1">Clients</span>
                    </a>
                </li>
                <li>
                    <a href="{{ url_for('admin.settings') }}" class="flex flex-col items-center p-2 rounded-xl hover:bg-green-700 group {% if request.endpoint == 'admin.settings' %}sidebar-icon-active{% endif %}">
                        <span class="text-2xl">⚙️</span>
                        <span class="text-xs mt-1">Settings</span>
                    </a>
                </li>
                {% elif user.role == 'client' %}
                <li>
                    <a href="{{ url_for('client.dashboard') }}" class="flex flex-col items-center p-2 rounded-xl hover:bg-green-700 group {% if request.endpoint == 'client.dashboard' %}sidebar-icon-active{% endif %}">
                        <span class="text-2xl">🏠</span>
                        <span class="text-xs mt-1">Home</span>
                    </a>
                </li>
                <li>
                    <a href="{{ url_for('client.property_list') }}" class="flex flex-col items-center p-2 rounded-xl hover:bg-green-700 group {% if request.endpoint == 'client.property_list' %}sidebar-icon-active{% endif %}">
                        <span class="text-2xl">🏢</span>
                        <span class="text-xs mt-1">Properties</span>
                    </a>
                </li>
                <li>
                    <a href="{{ url_for('client.service_list') }}" class="flex flex-col items-center p-2 rounded-xl hover:bg-green-700 group {% if request.endpoint == 'client.service_list' %}sidebar-icon-active{% endif %}">
                        <span class="text-2xl">🛠️</span>
                        <span class="text-xs mt-1">Services</span>
                    </a>
                </li>
                <li>
                    <a href="{{ url_for('client.payment_list') }}" class="flex flex-col items-center p-2 rounded-xl hover:bg-green-700 group {% if request.endpoint == 'client.payment_list' %}sidebar-icon-active{% endif %}">
                        <span class="text-2xl">💰</span>
                        <span class="text-xs mt-1">Payments</span>
                    </a>
                </li>
                <li>
                    <a href="{{ url_for('client.support') }}" class="flex flex-col items-center p-2 rounded-xl hover:bg-green-700 group {% if request.endpoint == 'client.support' %}sidebar-icon-active{% endif %}">
                        <span class="text-2xl">🆘</span>
                        <span class="text-xs mt-1">Support</span>
                    </a>
                </li>
                {% endif %}
            {% else %}
                <li>
                    <a href="{{ url_for('auth.login') }}" class="flex flex-col items-center p-2 rounded-xl hover:bg-green-700 group {% if request.endpoint == 'auth.login' %}sidebar-icon-active{% endif %}">
                        <span class="text-2xl">🔑</span>
                        <span class="text-xs mt-1">Login</span>
                    </a>
                </li>
            {% endif %}
        </ul>
        
        {% if user %}
        <div class="mt-auto">
            <a href="{{ url_for('auth.logout') }}" class="flex flex-col items-center p-2 rounded-xl hover:bg-green-700 group">
                <span class="text-2xl">🚪</span>
                <span class="text-xs mt-1">Logout</span>
            </a>
        </div>
        {% endif %}
    </nav>

    <!-- Mobile menu -->
    <div id="mobile-menu" class="fixed inset-0 bg-gray-800 bg-opacity-90 z-40 hidden flex flex-col justify-center items-center">
        <button id="close-mobile-menu" class="absolute top-4 right-4 text-white text-2xl">
            <i class="fas fa-times"></i>
        </button>
        <nav class="text-center">
            {% if user %}
                {% if user.role == 'staff' %}
                <a href="{{ url_for('staff.dashboard') }}" class="block py-3 text-xl text-white hover:text-primary-300">Dashboard</a>
                <a href="{{ url_for('staff.client_list') }}" class="block py-3 text-xl text-white hover:text-primary-300">Clients</a>
                <a href="{{ url_for('staff.property_list') }}" class="block py-3 text-xl text-white hover:text-primary-300">Properties</a>
                <a href="{{ url_for('staff.service_list') }}" class="block py-3 text-xl text-white hover:text-primary-300">Services</a>
                <a href="{{ url_for('staff.payment_list') }}" class="block py-3 text-xl text-white hover:text-primary-300">Payments</a>
                <a href="{{ url_for('staff.report_list') }}" class="block py-3 text-xl text-white hover:text-primary-300">Reports</a>
                {% elif user.role == 'super_user' %}
                <a href="{{ url_for('admin.dashboard') }}" class="block py-3 text-xl text-white hover:text-primary-300">Dashboard</a>
                <a href="{{ url_for('admin.staff_list') }}" class="block py-3 text-xl text-white hover:text-primary-300">Staff</a>
                <a href="{{ url_for('admin.client_list') }}" class="block py-3 text-xl text-white hover:text-primary-300">Clients</a>
                <a href="{{ url_for('admin.settings') }}" class="block py-3 text-xl text-white hover:text-primary-300">Settings</a>
                {% elif user.role == 'client' %}
                <a href="{{ url_for('client.dashboard') }}" class="block py-3 text-xl text-white hover:text-primary-300">Dashboard</a>
                <a href="{{ url_for('client.property_list') }}" class="block py-3 text-xl text-white hover:text-primary-300">My Properties</a>
                <a href="{{ url_for('client.service_list') }}" class="block py-3 text-xl text-white hover:text-primary-300">Services</a>
                <a href="{{ url_for('client.payment_list') }}" class="block py-3 text-xl text-white hover:text-primary-300">Payments</a>
                <a href="{{ url_for('client.support') }}" class="block py-3 text-xl text-white hover:text-primary-300">Support</a>
                {% endif %}
                <div class="border-t border-gray-700 mt-4 pt-4">
                    <a href="{{ url_for('auth.profile') }}" class="block py-3 text-xl text-white hover:text-primary-300">Profile</a>
                    <a href="{{ url_for('auth.change_password') }}" class="block py-3 text-xl text-white hover:text-primary-300">Change Password</a>
                    <a href="{{ url_for('auth.logout') }}" class="block py-3 text-xl text-white hover:text-primary-300">Logout</a>
                </div>
            {% else %}
                <a href="{{ url_for('auth.login') }}" class="block py-3 text-xl text-white hover:text-primary-300">Login</a>
                <a href="{{ url_for('index') }}" class="block py-3 text-xl text-white hover:text-primary-300">Home</a>
            {% endif %}
        </nav>
    </div>

    <!-- Main Content Area -->
    <main class="flex-1 overflow-y-auto p-4 lg:ml-20 transition-all duration-300 ease-in-out">
        <!-- Main Content Header -->
        <div class="flex flex-col sm:flex-row sm:items-center justify-between mb-6">
            <div class="flex items-center space-x-4">
                <h1 class="text-3xl font-bold text-gray-800">{% block header_title %}Green Peak Shelters{% endblock %}</h1>
                {% if user %}
                <span class="hidden lg:inline-block text-xl text-gray-700">🔍</span>
                <input type="text" placeholder="Search..." class="hidden lg:inline-block bg-gray-100 text-gray-700 px-4 py-2 rounded-xl focus:outline-none focus:ring-1 focus:ring-green-500">
                {% endif %}
            </div>
            <div class="mt-4 sm:mt-0 flex items-center space-x-4 text-sm">
                <span class="text-gray-500" id="current-date"></span>
                {% if user %}
                <div class="hidden lg:flex items-center space-x-2">
                    <span class="text-gray-700 text-xl">🔔</span>
                    <div class="w-8 h-8 rounded-full bg-[#224F34] text-white flex items-center justify-center text-sm">
                        {{ user.firstname[0] }}{{ user.lastname[0] }}
                    </div>
                </div>
                {% endif %}
            </div>
        </div>

        <!-- Flash Messages -->
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="mb-4 p-4 rounded-lg {% if category == 'error' %}bg-red-100 text-red-700{% elif category == 'success' %}bg-green-100 text-green-700{% else %}bg-blue-100 text-blue-700{% endif %} flex justify-between items-center">
                        <span>{{ message }}</span>
                        <button class="dismiss-button text-gray-500 hover:text-gray-700">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                {% endfor %}
            {% endif %}
        {% endwith %}
            
        <!-- Content Block -->
        {% block content %}{% endblock %}
    </main>

    <!-- Mobile menu button -->
    <div class="lg:hidden fixed bottom-4 right-4 z-50">
        <button id="mobile-menu-button" class="bg-[#224F34] text-white p-3 rounded-full shadow-lg">
            <i class="fas fa-bars"></i>
        </button>
    </div>

    <script>
        // Set current date
        document.addEventListener('DOMContentLoaded', function() {
            const options = { year: 'numeric', month: 'long', day: 'numeric' };
            const currentDate = new Date().toLocaleDateString('en-US', options);
            document.getElementById('current-date').textContent = currentDate;
            
            // Mobile menu toggle
            const mobileMenuButton = document.getElementById('mobile-menu-button');
            const closeMobileMenu = document.getElementById('close-mobile-menu');
            const mobileMenu = document.getElementById('mobile-menu');
            const sidebar = document.getElementById('sidebar');
            
            if (mobileMenuButton && closeMobileMenu && mobileMenu) {
                mobileMenuButton.addEventListener('click', function() {
                    mobileMenu.classList.remove('hidden');
                });
                
                closeMobileMenu.addEventListener('click', function() {
                    mobileMenu.classList.add('hidden');
                });
            }
            
            // Flash message dismissal
            const dismissButtons = document.querySelectorAll('.dismiss-button');
            dismissButtons.forEach(button => {
                button.addEventListener('click', function() {
                    this.parentElement.style.display = 'none';
                });
            });
        });
    </script>
    
    {% block scripts %}{% endblock %}
</body>
</html>
