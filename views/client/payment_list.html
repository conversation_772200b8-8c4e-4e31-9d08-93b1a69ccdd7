{% extends "layout.html" %}

{% block title %}My Payments - Green Peak Shelters Portal{% endblock %}
{% block header_title %}My Payments{% endblock %}
{% block header_subtitle %}View your payment history{% endblock %}

{% block content %}
<div class="flex justify-between items-center mb-6">
    <h1 class="text-2xl font-bold text-primary-700">My Payments</h1>
</div>

<div class="bg-white shadow-md rounded-lg overflow-hidden">
    <table class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
            <tr>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">ID</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Item</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Amount</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
            {% if payments %}
                {% for payment in payments %}
                    <tr>
                        <td class="px-6 py-4 whitespace-nowrap">{{ payment.id }}</td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            {% if payment.item_type == 'property' %}
                                {{ payment.related_item.name if payment.related_item else 'Unknown Property' }}
                            {% else %}
                                {{ payment.related_item.name if payment.related_item else 'Unknown Service' }}
                            {% endif %}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">{{ payment.item_type|capitalize }}</td>
                        <td class="px-6 py-4 whitespace-nowrap">₦{{ "{:,.2f}".format(payment.amount) }}</td>
                        <td class="px-6 py-4 whitespace-nowrap">{{ payment.payment_date.strftime('%Y-%m-%d') }}</td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full 
                                {% if payment.status == 'Completed' %}bg-green-100 text-green-800
                                {% elif payment.status == 'Pending' %}bg-yellow-100 text-yellow-800
                                {% else %}bg-red-100 text-red-800{% endif %}">
                                {{ payment.status }}
                            </span>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                            <a href="#" class="text-indigo-600 hover:text-indigo-900">View Receipt</a>
                        </td>
                    </tr>
                {% endfor %}
            {% else %}
                <tr>
                    <td colspan="7" class="px-6 py-4 text-center text-gray-500">You don't have any payment records yet.</td>
                </tr>
            {% endif %}
        </tbody>
    </table>
</div>

<div class="mt-4">
    <a href="{{ url_for('client.dashboard') }}" class="text-primary-600 hover:text-primary-800">
        &larr; Back to Dashboard
    </a>
</div>
{% endblock %}