{% extends "layout.html" %}

{% block title %}My Payments - Green Peak Shelters Portal{% endblock %}
{% block header_title %}My Payments{% endblock %}
{% block header_subtitle %}View your payment history{% endblock %}

{% block content %}
<div class="flex justify-between items-center mb-6">
    <h1 class="text-2xl font-bold text-primary-700">My Payments</h1>
</div>

<!-- Pending Payment Submissions -->
{% if pending_submissions %}
<div class="mb-8">
    <h2 class="text-xl font-semibold text-gray-800 mb-4">Payments Pending Approval</h2>
    <div class="bg-white shadow-md rounded-lg overflow-hidden">
        <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-yellow-50">
                <tr>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Submission ID</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Item</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Amount</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Payment Date</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Method</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Submitted</th>
                </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
                {% for submission in pending_submissions %}
                    <tr>
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                            #{{ submission.id }}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            {% if submission.item_type == 'property' %}
                                {{ submission.get_item_name() }}
                            {% else %}
                                {{ submission.get_item_name() }}
                            {% endif %}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            {{ submission.item_type|capitalize }}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            ₦{{ "{:,.2f}".format(submission.amount) }}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            {{ submission.payment_date.strftime('%Y-%m-%d') }}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            {{ submission.payment_method.replace('_', ' ')|title }}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full
                                {% if submission.status == 'approved' %}bg-green-100 text-green-800
                                {% elif submission.status == 'pending' %}bg-yellow-100 text-yellow-800
                                {% elif submission.status == 'rejected' %}bg-red-100 text-red-800
                                {% else %}bg-gray-100 text-gray-800{% endif %}">
                                {{ submission.status|title }}
                            </span>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {{ submission.created_at.strftime('%Y-%m-%d %H:%M') }}
                        </td>
                    </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
</div>
{% endif %}

<!-- Reviewed Payment Submissions -->
{% if reviewed_submissions %}
<div class="mb-8">
    <h2 class="text-xl font-semibold text-gray-800 mb-4">Payment Submission History</h2>
    <div class="bg-white shadow-md rounded-lg overflow-hidden">
        <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
                <tr>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Submission ID</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Item</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Amount</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Payment Date</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Reviewed</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Notes</th>
                </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
                {% for submission in reviewed_submissions %}
                    <tr>
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                            #{{ submission.id }}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            {{ submission.get_item_name() }}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            {{ submission.item_type|capitalize }}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            ₦{{ "{:,.2f}".format(submission.amount) }}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            {{ submission.payment_date.strftime('%Y-%m-%d') }}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full
                                {% if submission.status == 'approved' %}bg-green-100 text-green-800
                                {% elif submission.status == 'rejected' %}bg-red-100 text-red-800
                                {% else %}bg-gray-100 text-gray-800{% endif %}">
                                {{ submission.status|title }}
                            </span>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {% if submission.reviewed_at %}
                                {{ submission.reviewed_at.strftime('%Y-%m-%d %H:%M') }}
                            {% else %}
                                -
                            {% endif %}
                        </td>
                        <td class="px-6 py-4 text-sm text-gray-500 max-w-xs truncate">
                            {% if submission.review_notes %}
                                {{ submission.review_notes }}
                            {% else %}
                                -
                            {% endif %}
                        </td>
                    </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
</div>
{% endif %}

<!-- Confirmed Payments -->
<div class="mb-4">
    <h2 class="text-xl font-semibold text-gray-800 mb-4">Confirmed Payments</h2>
</div>

<div class="bg-white shadow-md rounded-lg overflow-hidden">
    <table class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
            <tr>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">ID</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Item</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Amount</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
            {% if payments %}
                {% for payment in payments %}
                    <tr>
                        <td class="px-6 py-4 whitespace-nowrap">{{ payment.id }}</td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            {% if payment.item_type == 'property' %}
                                {{ payment.related_item.name if payment.related_item else 'Unknown Property' }}
                            {% else %}
                                {{ payment.related_item.name if payment.related_item else 'Unknown Service' }}
                            {% endif %}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">{{ payment.item_type|capitalize }}</td>
                        <td class="px-6 py-4 whitespace-nowrap">₦{{ "{:,.2f}".format(payment.amount) }}</td>
                        <td class="px-6 py-4 whitespace-nowrap">{{ payment.payment_date.strftime('%Y-%m-%d') }}</td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full 
                                {% if payment.status == 'Completed' %}bg-green-100 text-green-800
                                {% elif payment.status == 'Pending' %}bg-yellow-100 text-yellow-800
                                {% else %}bg-red-100 text-red-800{% endif %}">
                                {{ payment.status }}
                            </span>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                            <a href="{{ url_for('client.payment_receipt', payment_id=payment.id) }}" class="text-indigo-600 hover:text-indigo-900">View Receipt</a>
                        </td>
                    </tr>
                {% endfor %}
            {% else %}
                <tr>
                    <td colspan="7" class="px-6 py-4 text-center text-gray-500">You don't have any payment records yet.</td>
                </tr>
            {% endif %}
        </tbody>
    </table>
</div>

<div class="mt-4">
    <a href="{{ url_for('client.dashboard') }}" class="text-primary-600 hover:text-primary-800">
        &larr; Back to Dashboard
    </a>
</div>
{% endblock %}