{% extends "layout.html" %}

{% block title %}Payment Receipt - Green Peak Shelters Portal{% endblock %}
{% block header_title %}Payment Receipt{% endblock %}
{% block header_subtitle %}Receipt #GP-{{ "%06d"|format(payment.id) }}{% endblock %}

{% block content %}
<div class="max-w-4xl mx-auto bg-white shadow-lg rounded-lg overflow-hidden">
    <!-- Header Section with Branding -->
    <div class="bg-gradient-to-r from-green-600 to-green-700 text-white p-8">
        <div class="flex items-center justify-between">
            <div class="flex items-center">
                <div class="w-16 h-16 bg-white rounded-full flex items-center justify-center mr-4">
                    <span class="text-green-600 font-bold text-xl">GP</span>
                </div>
                <div>
                    <h1 class="text-3xl font-bold">GREEN PEAK SHELTERS</h1>
                    <p class="text-green-100">Premium Property Management</p>
                </div>
            </div>
            <div class="text-right">
                <h2 class="text-2xl font-bold">PAYMENT RECEIPT</h2>
                <p class="text-green-100">Receipt #GP-{{ "%06d"|format(payment.id) }}</p>
            </div>
        </div>
    </div>

    <!-- Receipt Information -->
    <div class="p-8">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-8 mb-8">
            <!-- Receipt Details -->
            <div>
                <h3 class="text-lg font-semibold text-gray-700 mb-4 border-b border-gray-200 pb-2">Receipt Information</h3>
                <div class="space-y-3">
                    <div class="flex justify-between">
                        <span class="font-medium text-gray-600">Receipt Number:</span>
                        <span class="text-gray-800">GP-{{ "%06d"|format(payment.id) }}</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="font-medium text-gray-600">Date Issued:</span>
                        <span class="text-gray-800">{{ current_date.strftime('%B %d, %Y') }}</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="font-medium text-gray-600">Payment Date:</span>
                        <span class="text-gray-800">{{ payment.payment_date.strftime('%B %d, %Y') }}</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="font-medium text-gray-600">Status:</span>
                        <span class="px-2 py-1 text-xs font-semibold rounded-full 
                            {% if payment.status == 'Completed' %}bg-green-100 text-green-800
                            {% elif payment.status == 'Pending' %}bg-yellow-100 text-yellow-800
                            {% else %}bg-red-100 text-red-800{% endif %}">
                            {{ payment.status }}
                        </span>
                    </div>
                </div>
            </div>

            <!-- Client Information -->
            <div>
                <h3 class="text-lg font-semibold text-gray-700 mb-4 border-b border-gray-200 pb-2">Bill To</h3>
                <div class="space-y-3">
                    <div class="flex justify-between">
                        <span class="font-medium text-gray-600">Client Name:</span>
                        <span class="text-gray-800">{{ client.full_name }}</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="font-medium text-gray-600">Email:</span>
                        <span class="text-gray-800">{{ client.email }}</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="font-medium text-gray-600">Client ID:</span>
                        <span class="text-gray-800">GP-CLIENT-{{ "%04d"|format(client.id) }}</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Payment Details Table -->
        <div class="mb-8">
            <h3 class="text-lg font-semibold text-gray-700 mb-4 border-b border-gray-200 pb-2">Payment Details</h3>
            <div class="overflow-x-auto">
                <table class="min-w-full bg-white border border-gray-200 rounded-lg">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Amount</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr class="border-t border-gray-200">
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                {{ related_item.name if related_item else 'Unknown ' + payment.item_type }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                {{ payment.item_type|title }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 text-right font-medium">
                                ₦{{ "{:,.2f}".format(payment.amount) }}
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>

        <!-- Total Section -->
        <div class="bg-gray-50 p-6 rounded-lg mb-8">
            <div class="flex justify-between items-center">
                <span class="text-xl font-semibold text-gray-700">Total Amount:</span>
                <span class="text-2xl font-bold text-green-600">₦{{ "{:,.2f}".format(payment.amount) }}</span>
            </div>
        </div>

        <!-- Action Buttons -->
        <div class="flex flex-col sm:flex-row gap-4 justify-between items-center mb-8">
            <div class="flex gap-4">
                <a href="{{ url_for('client.payment_list') }}" 
                   class="px-4 py-2 bg-gray-500 hover:bg-gray-600 text-white rounded-lg transition-colors">
                    ← Back to Payments
                </a>
                <a href="{{ url_for('client.dashboard') }}" 
                   class="px-4 py-2 bg-primary-600 hover:bg-primary-700 text-white rounded-lg transition-colors">
                    Dashboard
                </a>
            </div>
            <div class="flex gap-4">
                <button onclick="window.print()" 
                        class="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors">
                    🖨️ Print Receipt
                </button>
                <a href="{{ url_for('client.payment_receipt_pdf', payment_id=payment.id) }}" 
                   class="px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded-lg transition-colors">
                    📄 Download PDF
                </a>
            </div>
        </div>

        <!-- Footer -->
        <div class="border-t border-gray-200 pt-6 text-center text-gray-600">
            <div class="mb-4">
                <h4 class="font-semibold text-gray-700">Green Peak Shelters</h4>
                <p class="text-sm">Thank you for your payment!</p>
                <p class="text-sm">For inquiries, please contact our office.</p>
            </div>
            <p class="text-xs text-gray-500 italic">This is a computer-generated receipt.</p>
        </div>
    </div>
</div>

<!-- Print Styles -->
<style>
@media print {
    .no-print {
        display: none !important;
    }
    
    body {
        background: white !important;
    }
    
    .shadow-lg {
        box-shadow: none !important;
    }
    
    .bg-gradient-to-r {
        background: #059669 !important;
        -webkit-print-color-adjust: exact;
        color-adjust: exact;
    }
    
    .rounded-lg {
        border-radius: 0 !important;
    }
    
    .max-w-4xl {
        max-width: 100% !important;
        margin: 0 !important;
    }
}
</style>
{% endblock %}

{% block scripts %}
<script>
    // Add moment.js functionality if not available
    if (typeof moment === 'undefined') {
        // Fallback for date formatting
        document.addEventListener('DOMContentLoaded', function() {
            const dateElements = document.querySelectorAll('[data-date]');
            dateElements.forEach(el => {
                const date = new Date();
                el.textContent = date.toLocaleDateString('en-US', { 
                    year: 'numeric', 
                    month: 'long', 
                    day: 'numeric' 
                });
            });
        });
    }
</script>
{% endblock %}
