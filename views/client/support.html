{% extends "layout.html" %}

{% block title %}Support Tickets - Green Peak Shelters Portal{% endblock %}
{% block header_title %}Support Tickets{% endblock %}
{% block header_subtitle %}View and manage your support requests{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <h1 class="text-2xl font-bold mb-6">My Support Tickets</h1>
    
    <div class="mb-4">
        <a href="{{ url_for('client.create_ticket') }}" class="bg-primary-500 hover:bg-primary-600 text-white font-bold py-2 px-4 rounded">
            Create New Ticket
        </a>
    </div>
    
    {% if tickets %}
        <div class="bg-white shadow-md rounded-lg overflow-hidden">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">ID</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Subject</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Created</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    {% for ticket in tickets %}
                    <tr>
                        <td class="px-6 py-4 whitespace-nowrap">{{ ticket.id }}</td>
                        <td class="px-6 py-4 whitespace-nowrap">{{ ticket.subject }}</td>
                        <td class="px-6 py-4 whitespace-nowrap">{{ ticket.status }}</td>
                        <td class="px-6 py-4 whitespace-nowrap">{{ ticket.created_at.strftime('%Y-%m-%d') }}</td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <a href="{{ url_for('client.view_ticket', ticket_id=ticket.id) }}" class="text-primary-500 hover:text-primary-700">View</a>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    {% else %}
        <div class="bg-white shadow-md rounded-lg p-6">
            <p class="text-gray-500">You don't have any support tickets yet.</p>
        </div>
    {% endif %}
</div>
{% endblock %}
