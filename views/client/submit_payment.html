{% extends "layout.html" %}

{% block title %}Submit Payment - Green Peak Shelters Portal{% endblock %}
{% block header_title %}Submit Payment{% endblock %}
{% block header_subtitle %}Submit payment details with proof of payment{% endblock %}

{% block content %}
<div class="max-w-4xl mx-auto bg-white shadow-lg rounded-lg overflow-hidden">
    <!-- Header Section -->
    <div class="bg-gradient-to-r from-green-600 to-green-700 text-white p-6">
        <div class="flex items-center">
            <div class="w-12 h-12 bg-white rounded-full flex items-center justify-center mr-4">
                <span class="text-green-600 font-bold text-lg">💳</span>
            </div>
            <div>
                <h1 class="text-2xl font-bold">Submit Payment</h1>
                <p class="text-green-100">{{ item_name }} - {{ item_type|title }}</p>
            </div>
        </div>
    </div>

    <!-- Form Section -->
    <div class="p-8">
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="mb-4 p-4 rounded-md {% if category == 'error' %}bg-red-50 text-red-700 border border-red-200{% else %}bg-green-50 text-green-700 border border-green-200{% endif %}">
                        {{ message }}
                    </div>
                {% endfor %}
            {% endif %}
        {% endwith %}

        <form method="POST" enctype="multipart/form-data" class="space-y-6">
            <!-- Item Information (Read-only) -->
            <div class="bg-gray-50 p-4 rounded-lg">
                <h3 class="text-lg font-semibold text-gray-700 mb-3">Payment For</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-600">Item</label>
                        <p class="text-gray-800 font-medium">{{ item_name }}</p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-600">Type</label>
                        <p class="text-gray-800 font-medium">{{ item_type|title }}</p>
                    </div>
                    {% if item_details %}
                        {% if item_type == 'property' %}
                            <div>
                                <label class="block text-sm font-medium text-gray-600">Property Value</label>
                                <p class="text-gray-800 font-medium">₦{{ "{:,.2f}".format(item_details.price) }}</p>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-600">Payment Cadence</label>
                                <p class="text-gray-800 font-medium">{{ item_details.payment_cadence|title }}</p>
                            </div>
                        {% elif item_type == 'service' %}
                            <div>
                                <label class="block text-sm font-medium text-gray-600">Service Cost</label>
                                <p class="text-gray-800 font-medium">₦{{ "{:,.2f}".format(item_details.cost) }}</p>
                            </div>
                        {% endif %}
                    {% endif %}
                </div>
            </div>

            <!-- Payment Details -->
            <div>
                <h3 class="text-lg font-semibold text-gray-700 mb-4">Payment Details</h3>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- Amount -->
                    <div>
                        <label for="amount" class="block text-sm font-medium text-gray-700 mb-2">Amount Paid *</label>
                        <div class="relative">
                            <span class="absolute left-3 top-3 text-gray-500">₦</span>
                            <input type="number" 
                                   id="amount" 
                                   name="amount" 
                                   step="0.01" 
                                   min="0" 
                                   required
                                   class="pl-8 w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500"
                                   placeholder="0.00">
                        </div>
                    </div>

                    <!-- Payment Date -->
                    <div>
                        <label for="payment_date" class="block text-sm font-medium text-gray-700 mb-2">Payment Date *</label>
                        <input type="date" 
                               id="payment_date" 
                               name="payment_date" 
                               required
                               max="{{ today }}"
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500">
                    </div>

                    <!-- Payment Method -->
                    <div>
                        <label for="payment_method" class="block text-sm font-medium text-gray-700 mb-2">Payment Method *</label>
                        <select id="payment_method" 
                                name="payment_method" 
                                required
                                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500">
                            <option value="">Select payment method</option>
                            <option value="bank_transfer">Bank Transfer</option>
                            <option value="online_payment">Online Payment</option>
                            <option value="cash">Cash</option>
                            <option value="check">Check</option>
                            <option value="mobile_money">Mobile Money</option>
                            <option value="other">Other</option>
                        </select>
                    </div>

                    <!-- Reference Number -->
                    <div>
                        <label for="reference_number" class="block text-sm font-medium text-gray-700 mb-2">Reference Number</label>
                        <input type="text" 
                               id="reference_number" 
                               name="reference_number" 
                               maxlength="100"
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500"
                               placeholder="Transaction ID, Check number, etc.">
                    </div>
                </div>
            </div>

            <!-- Proof of Payment -->
            <div>
                <h3 class="text-lg font-semibold text-gray-700 mb-4">Proof of Payment</h3>
                
                <div>
                    <label for="proof_file" class="block text-sm font-medium text-gray-700 mb-2">Upload Proof *</label>
                    <div class="mt-1 flex justify-center px-6 pt-5 pb-6 border-2 border-gray-300 border-dashed rounded-md hover:border-green-400 transition-colors">
                        <div class="space-y-1 text-center">
                            <svg class="mx-auto h-12 w-12 text-gray-400" stroke="currentColor" fill="none" viewBox="0 0 48 48">
                                <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                            </svg>
                            <div class="flex text-sm text-gray-600">
                                <label for="proof_file" class="relative cursor-pointer bg-white rounded-md font-medium text-green-600 hover:text-green-500 focus-within:outline-none focus-within:ring-2 focus-within:ring-offset-2 focus-within:ring-green-500">
                                    <span>Upload a file</span>
                                    <input id="proof_file" name="proof_file" type="file" class="sr-only" required accept="image/*,.pdf">
                                </label>
                                <p class="pl-1">or drag and drop</p>
                            </div>
                            <p class="text-xs text-gray-500">PNG, JPG, PDF up to 10MB</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Description -->
            <div>
                <label for="description" class="block text-sm font-medium text-gray-700 mb-2">Additional Notes</label>
                <textarea id="description" 
                          name="description" 
                          rows="3" 
                          maxlength="500"
                          class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500"
                          placeholder="Any additional information about this payment..."></textarea>
            </div>

            <!-- Submit Buttons -->
            <div class="flex justify-between items-center pt-6 border-t border-gray-200">
                <a href="{{ url_for('client.property_detail', property_id=item_id) if item_type == 'property' else url_for('client.dashboard') }}" 
                   class="text-gray-600 hover:text-gray-800">
                    ← Cancel
                </a>
                
                <button type="submit" 
                        class="bg-green-600 hover:bg-green-700 text-white font-bold py-3 px-6 rounded-lg transition-colors">
                    Submit Payment
                </button>
            </div>
        </form>
    </div>
</div>

<script>
// File upload preview
document.getElementById('proof_file').addEventListener('change', function(e) {
    const file = e.target.files[0];
    if (file) {
        const fileSize = file.size / 1024 / 1024; // Convert to MB
        if (fileSize > 10) {
            alert('File size must be less than 10MB');
            e.target.value = '';
            return;
        }
        
        // Update the upload area to show selected file
        const uploadArea = e.target.closest('.border-dashed');
        const fileName = file.name;
        uploadArea.innerHTML = `
            <div class="text-center">
                <svg class="mx-auto h-12 w-12 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
                <p class="mt-2 text-sm text-gray-600">
                    <span class="font-medium text-green-600">${fileName}</span> selected
                </p>
                <p class="text-xs text-gray-500">Click to change file</p>
            </div>
        `;
    }
});

// Set today's date as default and maximum
document.addEventListener('DOMContentLoaded', function() {
    const today = new Date().toISOString().split('T')[0];
    document.getElementById('payment_date').value = today;
});
</script>
{% endblock %}
