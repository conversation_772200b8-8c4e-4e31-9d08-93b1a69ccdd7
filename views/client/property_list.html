{% extends "layout.html" %}

{% block title %}My Properties - Green Peak Shelters Portal{% endblock %}
{% block header_title %}My Properties{% endblock %}
{% block header_subtitle %}View your property portfolio{% endblock %}

{% block content %}
<div class="flex justify-between items-center mb-6">
    <h1 class="text-2xl font-bold text-primary-700">My Properties</h1>
</div>

<div class="bg-white shadow-md rounded-lg overflow-hidden">
    <table class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
            <tr>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Property Name</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Location</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Price</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Details</th>
            </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
            {% if properties %}
                {% for property in properties %}
                    <tr>
                        <td class="px-6 py-4 whitespace-nowrap">{{ property.name }}</td>
                        <td class="px-6 py-4 whitespace-nowrap">{{ property.property_type }}</td>
                        <td class="px-6 py-4 whitespace-nowrap">{{ property.location }}</td>
                        <td class="px-6 py-4 whitespace-nowrap">₦{{ property.price }}</td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full 
                                {% if property.status == 'Available' %}bg-green-100 text-green-800
                                {% elif property.status == 'Sold' %}bg-red-100 text-red-800
                                {% else %}bg-yellow-100 text-yellow-800{% endif %}">
                                {{ property.status }}
                            </span>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                            <a href="#" class="text-indigo-600 hover:text-indigo-900">View Details</a>
                        </td>
                    </tr>
                {% endfor %}
            {% else %}
                <tr>
                    <td colspan="6" class="px-6 py-4 text-center text-gray-500">You don't have any properties yet.</td>
                </tr>
            {% endif %}
        </tbody>
    </table>
</div>

<div class="mt-4">
    <a href="{{ url_for('client.dashboard') }}" class="text-primary-600 hover:text-primary-800">
        &larr; Back to Dashboard
    </a>
</div>
{% endblock %}