{% extends "layout.html" %}

{% block title %}{{ property.name }} - Green Peak Shelters Portal{% endblock %}
{% block header_title %}Property Details{% endblock %}
{% block header_subtitle %}{{ property.name }}{% endblock %}

{% block content %}
<div class="mb-6">
    <h1 class="text-2xl font-bold text-primary-700">{{ property.name }}</h1>
    <p class="text-gray-600">Property ID: {{ property.id }}</p>
</div>

<div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
    <!-- Property Information -->
    <div class="bg-white shadow-md rounded-lg p-6">
        <h2 class="text-xl font-bold text-primary-700 mb-4">Property Information</h2>
        <div class="space-y-3">
            <div>
                <span class="font-semibold text-gray-700">Name:</span>
                <span class="text-gray-800">{{ property.name }}</span>
            </div>
            <div>
                <span class="font-semibold text-gray-700">Type:</span>
                <span class="text-gray-800">{{ property.property_type }}</span>
            </div>
            <div>
                <span class="font-semibold text-gray-700">Location:</span>
                <span class="text-gray-800">{{ property.location }}</span>
            </div>
            <div>
                <span class="font-semibold text-gray-700">Annual Price:</span>
                <span class="text-gray-800">₦{{ property.price|format_currency }}</span>
            </div>
            <div>
                <span class="font-semibold text-gray-700">Payment Cadence:</span>
                <span class="text-gray-800">{{ property.payment_cadence|title }}</span>
            </div>
            <div>
                <span class="font-semibold text-gray-700">Status:</span>
                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full 
                    {% if property.status == 'Available' %}bg-green-100 text-green-800
                    {% elif property.status == 'Sold' %}bg-red-100 text-red-800
                    {% else %}bg-yellow-100 text-yellow-800{% endif %}">
                    {{ property.status }}
                </span>
            </div>
            <div>
                <span class="font-semibold text-gray-700">Assigned Date:</span>
                <span class="text-gray-800">{{ client_property.created_at.strftime('%Y-%m-%d') }}</span>
            </div>
            {% if property.description %}
            <div>
                <span class="font-semibold text-gray-700">Description:</span>
                <p class="text-gray-800 mt-1">{{ property.description }}</p>
            </div>
            {% endif %}
        </div>
    </div>
    
    <!-- Payment Summary -->
    <div class="bg-white shadow-md rounded-lg p-6">
        <h2 class="text-xl font-bold text-primary-700 mb-4">Payment Summary</h2>
        <div class="space-y-4">
            <div class="bg-green-50 p-4 rounded-lg">
                <div class="flex justify-between items-center">
                    <span class="font-semibold text-green-700">Total Paid:</span>
                    <span class="text-2xl font-bold text-green-700">₦{{ "{:,.2f}".format(total_paid) }}</span>
                </div>
            </div>
            
            <div class="bg-red-50 p-4 rounded-lg">
                <div class="flex justify-between items-center">
                    <span class="font-semibold text-red-700">Amount Owed:</span>
                    <span class="text-2xl font-bold text-red-700">₦{{ "{:,.2f}".format(amount_owed) }}</span>
                </div>
            </div>
            
            <div class="bg-blue-50 p-4 rounded-lg">
                <div class="flex justify-between items-center">
                    <span class="font-semibold text-blue-700">Total Property Value:</span>
                    <span class="text-xl font-bold text-blue-700">₦{{ property.price|format_currency }}</span>
                </div>
            </div>
            
            {% if amount_owed > 0 %}
            <div class="mt-4 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
                <div class="flex items-center">
                    <svg class="h-5 w-5 text-yellow-400 mr-2" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                    </svg>
                    <div>
                        <h4 class="text-yellow-800 font-semibold">Payment Required</h4>
                        <p class="text-yellow-700 text-sm">Please contact our office to arrange payment for your outstanding balance.</p>
                    </div>
                </div>
            </div>
            {% else %}
            <div class="mt-4 p-4 bg-green-50 border border-green-200 rounded-lg">
                <div class="flex items-center">
                    <svg class="h-5 w-5 text-green-400 mr-2" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                    </svg>
                    <div>
                        <h4 class="text-green-800 font-semibold">Payments Up to Date</h4>
                        <p class="text-green-700 text-sm">All payments for this property are current.</p>
                    </div>
                </div>
            </div>
            {% endif %}
        </div>
    </div>
</div>

<!-- Payment History -->
<div class="bg-white shadow-md rounded-lg overflow-hidden mb-8">
    <div class="px-6 py-4 bg-gray-50 border-b border-gray-200">
        <h2 class="text-xl font-bold text-primary-700">Payment History</h2>
    </div>
    {% if payments %}
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Payment ID</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Amount</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    {% for payment in payments %}
                    <tr>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">#{{ payment.id }}</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">₦{{ "{:,.2f}".format(payment.amount) }}</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ payment.payment_date.strftime('%Y-%m-%d') }}</td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full 
                                {% if payment.status == 'Completed' %}bg-green-100 text-green-800
                                {% elif payment.status == 'Pending' %}bg-yellow-100 text-yellow-800
                                {% else %}bg-red-100 text-red-800{% endif %}">
                                {{ payment.status }}
                            </span>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    {% else %}
        <div class="px-6 py-4 text-center text-gray-500">
            No payments have been made for this property yet.
        </div>
    {% endif %}
</div>

<!-- Navigation -->
<div class="flex justify-between items-center">
    <a href="{{ url_for('client.property_list') }}" class="text-primary-600 hover:text-primary-800">
        &larr; Back to My Properties
    </a>

    <div class="flex space-x-4">
        <a href="{{ url_for('client.submit_payment', item_type='property', item_id=property.id) }}" class="bg-green-600 hover:bg-green-700 text-white font-bold py-2 px-4 rounded">
            💳 Make Payment
        </a>
        <a href="{{ url_for('client.dashboard') }}" class="bg-primary-600 hover:bg-primary-700 text-white font-bold py-2 px-4 rounded">
            Back to Dashboard
        </a>
    </div>
</div>
{% endblock %}
