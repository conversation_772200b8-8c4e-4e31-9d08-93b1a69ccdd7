{% extends "layout.html" %}

{% block title %}Support Ticket #{{ ticket.id }} - Green Peak Shelters Portal{% endblock %}
{% block header_title %}Support Ticket #{{ ticket.id }}{% endblock %}
{% block header_subtitle %}{{ ticket.subject }}{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <h1 class="text-2xl font-bold mb-2">Ticket #{{ ticket.id }}: {{ ticket.subject }}</h1>
    <p class="text-gray-600 mb-6">Status: {{ ticket.status }} | Created: {{ ticket.created_at.strftime('%Y-%m-%d %H:%M') }}</p>
    
    <div class="mb-4">
        <a href="{{ url_for('client.support') }}" class="text-primary-500 hover:text-primary-700">
            &larr; Back to Support Tickets
        </a>
    </div>
    
    {% with messages = get_flashed_messages(with_categories=true) %}
        {% if messages %}
            {% for category, message in messages %}
                <div class="p-4 mb-4 {% if category == 'error' %}bg-red-100 text-red-700{% else %}bg-green-100 text-green-700{% endif %} rounded">
                    {{ message }}
                </div>
            {% endfor %}
        {% endif %}
    {% endwith %}
    
    <div class="bg-white shadow-md rounded-lg p-6 mb-6">
        <h2 class="text-lg font-semibold mb-4">Conversation</h2>
        
        <div class="space-y-4">
            {% for message in messages %}
                <div class="p-4 rounded-lg {% if message.user_id == g.user.id %}bg-blue-50 ml-8{% else %}bg-gray-50 mr-8{% endif %}">
                    <div class="flex justify-between items-start mb-2">
                        <span class="font-medium">{{ message.user.firstname }} {{ message.user.lastname }}</span>
                        <span class="text-xs text-gray-500">{{ message.created_at.strftime('%Y-%m-%d %H:%M') }}</span>
                    </div>
                    <p class="text-gray-700">{{ message.message }}</p>
                </div>
            {% endfor %}
        </div>
    </div>
    
    {% if ticket.status != 'closed' %}
        <div class="bg-white shadow-md rounded-lg p-6">
            <h2 class="text-lg font-semibold mb-4">Reply to Ticket</h2>
            
            <form method="POST" action="{{ url_for('client.view_ticket', ticket_id=ticket.id) }}">
                <div class="mb-4">
                    <label for="message" class="block text-gray-700 font-medium mb-2">Your Message</label>
                    <textarea id="message" name="message" rows="4" required
                        class="w-full px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"></textarea>
                </div>
                
                <div class="flex justify-end">
                    <button type="submit" class="bg-primary-500 hover:bg-primary-600 text-white font-bold py-2 px-4 rounded">
                        Send Reply
                    </button>
                </div>
            </form>
        </div>
    {% else %}
        <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4 text-center">
            <p class="text-yellow-700">This ticket is closed. Contact support if you need further assistance.</p>
        </div>
    {% endif %}
</div>
{% endblock %}
