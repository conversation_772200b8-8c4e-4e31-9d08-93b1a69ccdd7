{% extends "layout.html" %}

{% block title %}Create Support Ticket - Green Peak Shelters Portal{% endblock %}
{% block header_title %}Create Support Ticket{% endblock %}
{% block header_subtitle %}Submit a new support request{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <h1 class="text-2xl font-bold mb-6">Create New Support Ticket</h1>
    
    <div class="mb-4">
        <a href="{{ url_for('client.support') }}" class="text-primary-500 hover:text-primary-700">
            &larr; Back to Support Tickets
        </a>
    </div>
    
    {% with messages = get_flashed_messages(with_categories=true) %}
        {% if messages %}
            {% for category, message in messages %}
                <div class="p-4 mb-4 {% if category == 'error' %}bg-red-100 text-red-700{% else %}bg-green-100 text-green-700{% endif %} rounded">
                    {{ message }}
                </div>
            {% endfor %}
        {% endif %}
    {% endwith %}
    
    <div class="bg-white shadow-md rounded-lg p-6">
        <form method="POST" action="{{ url_for('client.create_ticket') }}">
            <div class="mb-4">
                <label for="subject" class="block text-gray-700 font-medium mb-2">Subject</label>
                <input type="text" id="subject" name="subject" required
                    class="w-full px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500">
            </div>
            
            <div class="mb-4">
                <label for="message" class="block text-gray-700 font-medium mb-2">Message</label>
                <textarea id="message" name="message" rows="6" required
                    class="w-full px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"></textarea>
            </div>
            
            <div class="flex justify-end">
                <button type="submit" class="bg-primary-500 hover:bg-primary-600 text-white font-bold py-2 px-4 rounded">
                    Submit Ticket
                </button>
            </div>
        </form>
    </div>
</div>
{% endblock %}
