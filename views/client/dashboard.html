{% extends "layout.html" %}

{% block title %}Client Dashboard - Green Peak Shelters Portal{% endblock %}
{% block header_title %}Client Dashboard{% endblock %}
{% block header_subtitle %}Manage your properties and services{% endblock %}

{% block content %}
<!-- Summary Cards -->
<div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-8">
    <div class="bg-white p-6 rounded-lg shadow-md">
        <h3 class="text-xl font-bold text-primary-700 mb-2">My Properties</h3>
        <p class="text-3xl font-bold">{{ property_count }}</p>
        <a href="{{ url_for('client.property_list') }}" class="text-primary-600 hover:text-primary-800">View my properties →</a>
    </div>

    <div class="bg-white p-6 rounded-lg shadow-md">
        <h3 class="text-xl font-bold text-primary-700 mb-2">My Services</h3>
        <p class="text-3xl font-bold">{{ service_count }}</p>
        <a href="{{ url_for('client.service_list') }}" class="text-primary-600 hover:text-primary-800">View my services →</a>
    </div>

    <div class="bg-white p-6 rounded-lg shadow-md">
        <h3 class="text-xl font-bold text-primary-700 mb-2">Payment History</h3>
        <p class="text-3xl font-bold">₦{{ total_payments }}</p>
        <a href="{{ url_for('client.payment_list') }}" class="text-primary-600 hover:text-primary-800">View payment history →</a>
    </div>
</div>

<!-- Payment Analytics -->
<div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
    <div class="bg-white shadow-md rounded-lg overflow-hidden">
        <div class="px-6 py-4 bg-gray-50 border-b border-gray-200">
            <h2 class="text-xl font-bold text-primary-700">My Payment Overview</h2>
        </div>
        <div class="p-6">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div class="bg-green-50 p-6 rounded-lg text-center">
                    <p class="text-green-700 text-sm font-medium">Total Amount Paid</p>
                    <h3 class="text-3xl font-bold text-green-700">₦{{ "{:,.2f}".format(total_amount_paid) }}</h3>
                    <div class="mt-2 text-xs text-green-600">
                        <div>Properties: ₦{{ "{:,.2f}".format(total_paid_properties) }}</div>
                        <div>Services: ₦{{ "{:,.2f}".format(total_paid_services) }}</div>
                    </div>
                </div>
                <div class="bg-red-50 p-6 rounded-lg text-center">
                    <p class="text-red-700 text-sm font-medium">Total Amount Owed</p>
                    <h3 class="text-3xl font-bold text-red-700">₦{{ "{:,.2f}".format(total_amount_owed) }}</h3>
                    <div class="mt-2 text-xs text-red-600">
                        <div>Properties: ₦{{ "{:,.2f}".format(total_owed_properties) }}</div>
                        <div>Services: ₦{{ "{:,.2f}".format(total_owed_services) }}</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="bg-white shadow-md rounded-lg overflow-hidden">
        <div class="px-6 py-4 bg-gray-50 border-b border-gray-200">
            <h2 class="text-xl font-bold text-primary-700">Payment Status</h2>
        </div>
        <div class="p-6">
            <div class="h-64">
                <canvas id="paymentStatusChart"></canvas>
            </div>
        </div>
    </div>
</div>

<!-- Payment Breakdown -->
<div class="bg-white shadow-md rounded-lg overflow-hidden mb-8">
    <div class="px-6 py-4 bg-gray-50 border-b border-gray-200">
        <h2 class="text-xl font-bold text-primary-700">Payment Breakdown</h2>
    </div>
    <div class="p-6">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
                <h3 class="text-lg font-semibold text-gray-700 mb-4">Properties vs Services (Paid)</h3>
                <div class="h-64">
                    <canvas id="paidBreakdownChart"></canvas>
                </div>
            </div>
            <div>
                <h3 class="text-lg font-semibold text-gray-700 mb-4">Properties vs Services (Owed)</h3>
                <div class="h-64">
                    <canvas id="owedBreakdownChart"></canvas>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Payment Status Summary -->
<div class="bg-white shadow-md rounded-lg overflow-hidden mb-8">
    <div class="px-6 py-4 bg-gray-50 border-b border-gray-200">
        <h2 class="text-xl font-bold text-primary-700">Financial Summary</h2>
    </div>
    <div class="p-6">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div class="bg-blue-50 p-6 rounded-lg">
                <h3 class="text-lg font-semibold text-blue-700 mb-4">Properties</h3>
                <div class="space-y-2">
                    <div class="flex justify-between">
                        <span class="text-blue-600">Total Paid:</span>
                        <span class="font-semibold text-blue-700">₦{{ "{:,.2f}".format(total_paid_properties) }}</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-blue-600">Amount Owed:</span>
                        <span class="font-semibold text-red-700">₦{{ "{:,.2f}".format(total_owed_properties) }}</span>
                    </div>
                </div>
            </div>
            <div class="bg-purple-50 p-6 rounded-lg">
                <h3 class="text-lg font-semibold text-purple-700 mb-4">Services</h3>
                <div class="space-y-2">
                    <div class="flex justify-between">
                        <span class="text-purple-600">Total Paid:</span>
                        <span class="font-semibold text-green-700">₦{{ "{:,.2f}".format(total_paid_services) }}</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-purple-600">Amount Owed:</span>
                        <span class="font-semibold text-red-700">₦{{ "{:,.2f}".format(total_owed_services) }}</span>
                    </div>
                </div>
            </div>
        </div>

        {% if total_amount_owed > 0 %}
        <div class="mt-6 p-4 bg-red-50 border border-red-200 rounded-lg">
            <div class="flex items-center">
                <svg class="h-5 w-5 text-red-400 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                </svg>
                <div>
                    <h4 class="text-red-800 font-semibold">Outstanding Balance</h4>
                    <p class="text-red-700">You have a total outstanding balance of ₦{{ "{:,.2f}".format(total_amount_owed) }}. Please contact our office to arrange payment.</p>
                </div>
            </div>
        </div>
        {% else %}
        <div class="mt-6 p-4 bg-green-50 border border-green-200 rounded-lg">
            <div class="flex items-center">
                <svg class="h-5 w-5 text-green-400 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                </svg>
                <div>
                    <h4 class="text-green-800 font-semibold">All Payments Up to Date</h4>
                    <p class="text-green-700">Congratulations! You have no outstanding balances.</p>
                </div>
            </div>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}

{% block scripts %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Payment Status Chart (Paid vs Owed)
        const statusCtx = document.getElementById('paymentStatusChart').getContext('2d');
        const statusChart = new Chart(statusCtx, {
            type: 'doughnut',
            data: {
                labels: ['Amount Paid', 'Amount Owed'],
                datasets: [{
                    data: [
                        {{ total_amount_paid }},
                        {{ total_amount_owed }}
                    ],
                    backgroundColor: [
                        'rgba(22, 163, 74, 0.6)',
                        'rgba(220, 38, 38, 0.6)'
                    ],
                    borderColor: [
                        'rgba(22, 163, 74, 1)',
                        'rgba(220, 38, 38, 1)'
                    ],
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                return context.label + ': ₦' + context.parsed.toLocaleString();
                            }
                        }
                    },
                    legend: {
                        position: 'bottom'
                    }
                }
            }
        });

        // Paid Breakdown Chart (Properties vs Services)
        const paidCtx = document.getElementById('paidBreakdownChart').getContext('2d');
        const paidChart = new Chart(paidCtx, {
            type: 'doughnut',
            data: {
                labels: ['Properties', 'Services'],
                datasets: [{
                    data: [
                        {{ total_paid_properties }},
                        {{ total_paid_services }}
                    ],
                    backgroundColor: [
                        'rgba(59, 130, 246, 0.6)',
                        'rgba(147, 51, 234, 0.6)'
                    ],
                    borderColor: [
                        'rgba(59, 130, 246, 1)',
                        'rgba(147, 51, 234, 1)'
                    ],
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                return context.label + ': ₦' + context.parsed.toLocaleString();
                            }
                        }
                    },
                    legend: {
                        position: 'bottom'
                    }
                }
            }
        });

        // Owed Breakdown Chart (Properties vs Services)
        const owedCtx = document.getElementById('owedBreakdownChart').getContext('2d');
        const owedChart = new Chart(owedCtx, {
            type: 'doughnut',
            data: {
                labels: ['Properties', 'Services'],
                datasets: [{
                    data: [
                        {{ total_owed_properties }},
                        {{ total_owed_services }}
                    ],
                    backgroundColor: [
                        'rgba(220, 38, 38, 0.6)',
                        'rgba(245, 101, 101, 0.6)'
                    ],
                    borderColor: [
                        'rgba(220, 38, 38, 1)',
                        'rgba(245, 101, 101, 1)'
                    ],
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                return context.label + ': ₦' + context.parsed.toLocaleString();
                            }
                        }
                    },
                    legend: {
                        position: 'bottom'
                    }
                }
            }
        });
    });
</script>
{% endblock %}
