{% extends "layout.html" %}

{% block title %}Client Details - Green Peak Shelters Portal{% endblock %}
{% block header_title %}Client Details{% endblock %}
{% block header_subtitle %}View client information{% endblock %}

{% block content %}
<div class="bg-white p-6 rounded-lg shadow-md">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold text-gray-800">{{ client.firstname }} {{ client.lastname }}</h2>
        <div>
            <a href="{{ url_for('admin.edit_client', client_id=client.id) }}" class="bg-indigo-600 hover:bg-indigo-700 text-white font-bold py-2 px-4 rounded mr-2">
                Edit Client
            </a>
            <a href="{{ url_for('admin.client_list') }}" class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded">
                Back to List
            </a>
        </div>
    </div>
    
    <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
        <div class="bg-gray-50 p-4 rounded-lg">
            <h3 class="text-lg font-semibold text-gray-700 mb-3">Client Information</h3>
            <div class="space-y-2">
                <p><span class="font-medium">Email:</span> {{ client.email }}</p>
                <p><span class="font-medium">Phone:</span> {{ client.phone or 'Not provided' }}</p>
                <p><span class="font-medium">Address:</span> {{ client.address or 'Not provided' }}</p>
                <p><span class="font-medium">Status:</span> 
                    {% if client.active %}
                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">Active</span>
                    {% else %}
                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">Inactive</span>
                    {% endif %}
                </p>
                <!-- Removed the created_at field that was causing the error -->
            </div>
        </div>
        
        <div class="bg-gray-50 p-4 rounded-lg">
            <h3 class="text-lg font-semibold text-gray-700 mb-3">Account Summary</h3>
            <div class="grid grid-cols-2 gap-4">
                <div class="bg-white p-3 rounded shadow-sm">
                    <p class="text-sm text-gray-500">Properties</p>
                    <p class="text-2xl font-bold">{{ properties|length }}</p>
                </div>
                <div class="bg-white p-3 rounded shadow-sm">
                    <p class="text-sm text-gray-500">Services</p>
                    <p class="text-2xl font-bold">{{ services|length }}</p>
                </div>
                <div class="bg-white p-3 rounded shadow-sm col-span-2">
                    <p class="text-sm text-gray-500">Last Login</p>
                    <p class="text-xl font-bold">{{ client.last_login.strftime('%Y-%m-%d %H:%M') if client.last_login else 'Never' }}</p>
                </div>
            </div>
        </div>
    </div>
    
    <div class="mb-8">
        <h3 class="text-lg font-semibold text-gray-700 mb-3">Properties</h3>
        {% if properties %}
        <div class="overflow-x-auto">
            <table class="min-w-full bg-white">
                <thead class="bg-gray-100">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Address</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                    </tr>
                </thead>
                <tbody class="divide-y divide-gray-200">
                    {% for property in properties %}
                    <tr>
                        <td class="px-6 py-4 whitespace-nowrap">{{ property.address }}</td>
                        <td class="px-6 py-4 whitespace-nowrap">{{ property.type }}</td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full 
                                {% if property.status == 'Active' %}bg-green-100 text-green-800
                                {% elif property.status == 'Pending' %}bg-yellow-100 text-yellow-800
                                {% else %}bg-red-100 text-red-800{% endif %}">
                                {{ property.status }}
                            </span>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                            <a href="#" class="text-indigo-600 hover:text-indigo-900">View</a>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
        <p class="text-gray-500">This client has no properties.</p>
        {% endif %}
    </div>
    
    <div class="mb-8">
        <h3 class="text-lg font-semibold text-gray-700 mb-3">Recent Payments</h3>
        {% if payments %}
        <div class="overflow-x-auto">
            <table class="min-w-full bg-white">
                <thead class="bg-gray-100">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Amount</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Method</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                    </tr>
                </thead>
                <tbody class="divide-y divide-gray-200">
                    {% for payment in payments %}
                    <tr>
                        <td class="px-6 py-4 whitespace-nowrap">{{ payment.payment_date.strftime('%Y-%m-%d') }}</td>
                        <td class="px-6 py-4 whitespace-nowrap">${{ "%.2f"|format(payment.amount) }}</td>
                        <td class="px-6 py-4 whitespace-nowrap">{{ payment.payment_method }}</td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full 
                                {% if payment.status == 'Completed' %}bg-green-100 text-green-800
                                {% elif payment.status == 'Pending' %}bg-yellow-100 text-yellow-800
                                {% else %}bg-red-100 text-red-800{% endif %}">
                                {{ payment.status }}
                            </span>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
        <p class="text-gray-500">This client has no payment history.</p>
        {% endif %}
    </div>
    
    <div class="mt-4">
        <a href="{{ url_for('admin.client_list') }}" class="text-green-600 hover:text-green-800">
            &larr; Back to Client List
        </a>
    </div>
</div>
{% endblock %}
