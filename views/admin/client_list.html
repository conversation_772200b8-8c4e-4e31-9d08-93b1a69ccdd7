{% extends "layout.html" %}

{% block title %}Client Management - Green Peak Shelters Portal{% endblock %}
{% block header_title %}Client Management{% endblock %}
{% block header_subtitle %}View and manage clients{% endblock %}

{% block content %}
<div class="bg-white p-6 rounded-lg shadow-md">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold text-gray-800">All Clients</h2>
    </div>
    
    <div class="overflow-x-auto">
        <table class="min-w-full bg-white">
            <thead class="bg-gray-100">
                <tr>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Email</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                </tr>
            </thead>
            <tbody class="divide-y divide-gray-200">
                {% if clients %}
                    {% for client in clients %}
                        <tr>
                            <td class="px-6 py-4 whitespace-nowrap">{{ client.firstname }} {{ client.lastname }}</td>
                            <td class="px-6 py-4 whitespace-nowrap">{{ client.email }}</td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                {% if client.active %}
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">Active</span>
                                {% else %}
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">Inactive</span>
                                {% endif %}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <a href="{{ url_for('admin.client_detail', client_id=client.id) }}" class="text-indigo-600 hover:text-indigo-900 mr-3">View</a>
                                <a href="{{ url_for('admin.edit_client', client_id=client.id) }}" class="text-green-600 hover:text-green-900 mr-3">Edit</a>
                            </td>
                        </tr>
                    {% endfor %}
                {% else %}
                    <tr>
                        <td colspan="4" class="px-6 py-4 text-center text-gray-500">No clients found</td>
                    </tr>
                {% endif %}
            </tbody>
        </table>
    </div>
    
    <div class="mt-4">
        <a href="{{ url_for('admin.dashboard') }}" class="text-green-600 hover:text-green-800">
            &larr; Back to Dashboard
        </a>
    </div>
</div>
{% endblock %}
