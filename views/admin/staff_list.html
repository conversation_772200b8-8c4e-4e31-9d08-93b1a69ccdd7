{% extends "layout.html" %}

{% block title %}Staff Management - Green Peak Shelters Portal{% endblock %}
{% block header_title %}Staff Management{% endblock %}
{% block header_subtitle %}View and manage staff accounts{% endblock %}

{% block content %}
<div class="flex justify-between items-center mb-6">
    <h1 class="text-2xl font-bold text-primary-700">Staff List</h1>
    <a href="{{ url_for('admin.create_staff') }}" class="bg-green-600 hover:bg-green-700 text-white font-bold py-2 px-4 rounded">
        Add New Staff
    </a>
</div>

<div class="bg-white shadow-md rounded-lg overflow-hidden">
    <table class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
            <tr>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Email</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Department</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
            {% if staff %}
                {% for member in staff %}
                    <tr>
                        <td class="px-6 py-4 whitespace-nowrap">{{ member.firstname }} {{ member.lastname }}</td>
                        <td class="px-6 py-4 whitespace-nowrap">{{ member.email }}</td>
                        <td class="px-6 py-4 whitespace-nowrap">{{ member.department or 'N/A' }}</td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            {% if member.active %}
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">Active</span>
                            {% else %}
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">Inactive</span>
                            {% endif %}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                            <a href="{{ url_for('admin.edit_staff', staff_id=member.id) }}" class="text-indigo-600 hover:text-indigo-900 mr-3">Edit</a>
                            <a href="#" class="text-red-600 hover:text-red-900">Delete</a>
                        </td>
                    </tr>
                {% endfor %}
            {% else %}
                <tr>
                    <td colspan="5" class="px-6 py-4 text-center text-gray-500">No staff members found</td>
                </tr>
            {% endif %}
        </tbody>
    </table>
</div>

<div class="mt-4">
    <a href="{{ url_for('admin.dashboard') }}" class="text-green-600 hover:text-green-800">
        &larr; Back to Dashboard
    </a>
</div>
{% endblock %}
