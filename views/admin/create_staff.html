{% extends "layout.html" %}

{% block title %}Create Staff - Green Peak Shelters Portal{% endblock %}
{% block header_title %}Create Staff Account{% endblock %}
{% block header_subtitle %}Add a new staff member to the system{% endblock %}

{% block content %}
<div class="bg-white shadow-md rounded-lg p-6 max-w-2xl mx-auto">
    <h1 class="text-2xl font-bold text-primary-700 mb-6">Create New Staff Account</h1>
    
    {% with messages = get_flashed_messages(with_categories=true) %}
        {% if messages %}
            {% for category, message in messages %}
                <div class="p-4 mb-4 {% if category == 'error' %}bg-red-100 text-red-700{% else %}bg-green-100 text-green-700{% endif %} rounded">
                    {{ message }}
                </div>
            {% endfor %}
        {% endif %}
    {% endwith %}
    
    <form method="POST" action="{{ url_for('admin.create_staff') }}">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
            <div>
                <label for="firstname" class="block text-gray-700 font-medium mb-2">First Name</label>
                <input type="text" id="firstname" name="firstname" required
                    class="w-full px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500">
            </div>
            <div>
                <label for="lastname" class="block text-gray-700 font-medium mb-2">Last Name</label>
                <input type="text" id="lastname" name="lastname" required
                    class="w-full px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500">
            </div>
        </div>
        
        <div class="mb-4">
            <label for="email" class="block text-gray-700 font-medium mb-2">Email Address</label>
            <input type="email" id="email" name="email" required
                class="w-full px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500">
        </div>

        <div class="mb-4">
            <label for="phone" class="block text-gray-700 font-medium mb-2">Phone Number</label>
            <input type="tel" id="phone" name="phone"
                class="w-full px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500">
        </div>

        <div class="mb-4">
            <label for="address" class="block text-gray-700 font-medium mb-2">Address</label>
            <textarea id="address" name="address" rows="3"
                class="w-full px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"></textarea>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
            <div>
                <label for="designation" class="block text-gray-700 font-medium mb-2">Designation</label>
                <input type="text" id="designation" name="designation"
                    class="w-full px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500">
            </div>
            <div>
                <label for="department" class="block text-gray-700 font-medium mb-2">Department</label>
                <input type="text" id="department" name="department"
                    class="w-full px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500">
            </div>
        </div>
        
        <div class="flex justify-between mt-6">
            <a href="{{ url_for('admin.staff_list') }}" class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded">
                Cancel
            </a>
            <button type="submit" class="bg-primary-600 hover:bg-primary-700 text-white font-bold py-2 px-4 rounded">
                Create Staff Account
            </button>
        </div>
    </form>
</div>
{% endblock %}
