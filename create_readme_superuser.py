#!/usr/bin/env python3
"""
Create the superuser account as specified in the README file
"""

from app import app
from models.database import db
from models.user import User

def create_readme_superuser():
    """Create the superuser account with credentials from README"""
    
    with app.app_context():
        print("=== Creating Superuser Account from README ===\n")
        
        # Credentials from README file
        email = "<EMAIL>"
        password = "Gr33nP3@k5hel+3rs2025"
        firstname = "System"
        lastname = "Administrator"
        
        # Check if superuser already exists
        existing_user = User.query.filter_by(email=email).first()
        if existing_user:
            print(f"✅ Superuser already exists!")
            print(f"   Email: {existing_user.email}")
            print(f"   Name: {existing_user.firstname} {existing_user.lastname}")
            print(f"   Role: {existing_user.role}")
            print(f"   Active: {existing_user.active}")
            
            # Check password type
            password_info = existing_user.get_password_strength_info()
            print(f"   Password Security: {password_info['type']} - {password_info['strength']}")
            
            return True
        
        print("Creating superuser account...")
        print(f"Email: {email}")
        print(f"Password: {password}")
        
        try:
            # Create the superuser
            superuser = User(
                email=email,
                firstname=firstname,
                lastname=lastname,
                role='super_user',
                active=True,
                designation="System Administrator",
                department="Administration"
            )
            
            # Set password using bcrypt
            superuser.set_password(password)
            
            # Add to database
            db.session.add(superuser)
            db.session.commit()
            
            print(f"\n✅ Superuser account created successfully!")
            print(f"\nAccount Details:")
            print(f"   Email: {email}")
            print(f"   Name: {firstname} {lastname}")
            print(f"   Role: Super User")
            print(f"   Designation: System Administrator")
            print(f"   Department: Administration")
            print(f"   Active: True")
            
            # Verify password strength
            password_info = superuser.get_password_strength_info()
            print(f"   Password Security: {password_info['type']} - {password_info['strength']}")
            
            print(f"\n🎉 You can now log in with these credentials:")
            print(f"   URL: http://127.0.0.1:5000/login")
            print(f"   Email: {email}")
            print(f"   Password: {password}")
            
            return True
            
        except Exception as e:
            print(f"❌ Error creating superuser: {e}")
            db.session.rollback()
            return False

if __name__ == '__main__':
    success = create_readme_superuser()
    if not success:
        print(f"\n❌ Failed to create superuser account.")
        exit(1)
