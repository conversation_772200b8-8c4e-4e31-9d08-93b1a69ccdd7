#!/usr/bin/env python3
"""
Correct calculation of how much money <PERSON> (<PERSON>) owes
This script fixes the flawed logic in the Property model
"""

from flask import Flask
from models.database import db
from models.user import User
from models.property import Property
from models.service import Service
from models.payment import Payment
from models.client_property import ClientProperty
from models.client_service import ClientService
from datetime import date

def create_app():
    """Create Flask app with database configuration"""
    app = Flask(__name__)
    app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///greenpeak.db'
    app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
    db.init_app(app)
    return app

def calculate_correct_debt():
    """Calculate Emma's debt with correct logic"""
    app = create_app()
    
    with app.app_context():
        print("=== CORRECTED Emma's Debt Analysis ===\n")
        
        # Find Emma
        emma = User.query.filter_by(email='<EMAIL>').first()
        if not emma:
            print("Emma not found in database!")
            return
        
        print(f"Client: {emma.full_name} ({emma.email})")
        print(f"Role: {emma.role}")
        print(f"Active: {emma.active}\n")
        
        # Find Emma's properties
        client_properties = ClientProperty.query.filter_by(client_id=emma.id).all()
        
        if not client_properties:
            print("Emma has no assigned properties.")
            return
        
        total_debt = 0
        today = date.today()
        
        print("--- Property Assignments (CORRECTED CALCULATION) ---")
        for cp in client_properties:
            property_obj = Property.query.filter_by(id=cp.property_id).first()
            if property_obj:
                print(f"\nProperty: {property_obj.name}")
                print(f"Location: {property_obj.location}")
                print(f"Type: {property_obj.property_type}")
                print(f"Annual Price: ${property_obj.price:,.2f}")
                print(f"Payment Cadence: {property_obj.payment_cadence}")
                print(f"Assigned Date: {cp.created_at}")
                
                # Get payments for this property by Emma
                property_payments = Payment.query.filter_by(
                    client_id=emma.id,
                    item_type='property',
                    item_id=property_obj.id
                ).all()
                
                total_paid = sum(payment.amount for payment in property_payments)
                print(f"Total Paid: ${total_paid:,.2f}")
                
                # CORRECT CALCULATION FOR ANNUAL PAYMENTS
                if property_obj.payment_cadence == 'annual':
                    # For annual payments, calculate how many years since assignment
                    assignment_date = cp.created_at.date()
                    years_since_assignment = today.year - assignment_date.year
                    
                    # If we're in the same year as assignment, client owes for this year
                    if years_since_assignment == 0:
                        # Same year as assignment - client owes for this year
                        total_expected = property_obj.price
                        print(f"Years since assignment: 0 (assigned this year)")
                        print(f"Expected payment for {today.year}: ${total_expected:,.2f}")
                    else:
                        # Multiple years - calculate total expected
                        total_expected = property_obj.price * (years_since_assignment + 1)
                        print(f"Years since assignment: {years_since_assignment}")
                        print(f"Expected payments for {years_since_assignment + 1} year(s): ${total_expected:,.2f}")
                    
                    amount_due = max(0, total_expected - total_paid)
                    
                elif property_obj.payment_cadence == 'one_time':
                    # For one-time payments
                    amount_due = max(0, property_obj.price - total_paid)
                    print(f"One-time payment expected: ${property_obj.price:,.2f}")
                    
                else:
                    # For other payment cadences, use the existing logic
                    amount_due = property_obj.get_amount_due()
                
                print(f"Amount Due: ${amount_due:,.2f}")
                
                # Determine correct status
                if amount_due == 0:
                    status = "✅ Up to Date"
                else:
                    status = f"❌ Owes ${amount_due:,.2f}"
                
                print(f"CORRECT Status: {status}")
                
                if property_payments:
                    print("Payment History:")
                    for payment in sorted(property_payments, key=lambda p: p.created_at, reverse=True):
                        print(f"  - ${payment.amount:,.2f} on {payment.payment_date} (Status: {payment.status})")
                
                total_debt += amount_due
        
        # Check for services (same logic as before)
        client_services = ClientService.query.filter_by(client_id=emma.id).all()
        
        if client_services:
            print("\n--- Service Assignments ---")
            for cs in client_services:
                service_obj = Service.query.filter_by(id=cs.service_id).first()
                if service_obj:
                    print(f"\nService: {service_obj.name}")
                    print(f"Price: ${service_obj.price:,.2f}")
                    print(f"Assigned Date: {cs.created_at}")
                    
                    # Get payments for this service by Emma
                    service_payments = Payment.query.filter_by(
                        client_id=emma.id,
                        item_type='service',
                        item_id=service_obj.id
                    ).all()
                    
                    total_paid = sum(payment.amount for payment in service_payments)
                    print(f"Total Paid: ${total_paid:,.2f}")
                    
                    # For services, assume one-time payment
                    amount_due = max(0, service_obj.price - total_paid)
                    print(f"Amount Due: ${amount_due:,.2f}")
                    
                    if service_payments:
                        print("Payment History:")
                        for payment in sorted(service_payments, key=lambda p: p.created_at, reverse=True):
                            print(f"  - ${payment.amount:,.2f} on {payment.payment_date} (Status: {payment.status})")
                    
                    total_debt += amount_due
        
        print(f"\n" + "="*50)
        print(f"=== CORRECTED TOTAL DEBT ===")
        print(f"Emma owes: ${total_debt:,.2f}")
        print(f"="*50)
        
        if total_debt == 0:
            print("🎉 Emma is up to date with all payments!")
        elif total_debt > 0:
            print("⚠️  Emma has outstanding debt that needs to be paid.")

if __name__ == "__main__":
    calculate_correct_debt()
