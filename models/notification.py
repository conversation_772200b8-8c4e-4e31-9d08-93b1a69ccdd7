from models.database import db
from datetime import datetime

class Notification(db.Model):
    """Model for system notifications"""
    __tablename__ = 'notifications'
    
    id = db.<PERSON>umn(db.Integer, primary_key=True)
    
    # Notification details
    title = db.Column(db.String(200), nullable=False)
    message = db.Column(db.Text, nullable=False)
    notification_type = db.Column(db.String(50), default='info')  # 'info', 'success', 'warning', 'error'
    
    # Target user (if None, it's for all staff)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'))
    
    # Related object information
    related_type = db.Column(db.String(50))  # 'payment_submission', 'support_ticket', etc.
    related_id = db.Column(db.Integer)  # ID of the related object
    
    # Status
    is_read = db.Column(db.<PERSON>, default=False)
    
    # Timestamps
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    read_at = db.Column(db.DateTime)
    
    # Relationships
    user = db.relationship('User', backref='notifications')
    
    def __repr__(self):
        return f'<Notification {self.id}: {self.title}>'
    
    def mark_as_read(self):
        """Mark notification as read"""
        self.is_read = True
        self.read_at = datetime.utcnow()
        db.session.commit()
    
    def get_type_badge_class(self):
        """Get CSS class for notification type badge"""
        type_classes = {
            'info': 'bg-blue-100 text-blue-800',
            'success': 'bg-green-100 text-green-800',
            'warning': 'bg-yellow-100 text-yellow-800',
            'error': 'bg-red-100 text-red-800'
        }
        return type_classes.get(self.notification_type, 'bg-gray-100 text-gray-800')
    
    def get_time_ago(self):
        """Get human-readable time since creation"""
        now = datetime.utcnow()
        diff = now - self.created_at
        
        if diff.days > 0:
            return f"{diff.days} day{'s' if diff.days > 1 else ''} ago"
        elif diff.seconds > 3600:
            hours = diff.seconds // 3600
            return f"{hours} hour{'s' if hours > 1 else ''} ago"
        elif diff.seconds > 60:
            minutes = diff.seconds // 60
            return f"{minutes} minute{'s' if minutes > 1 else ''} ago"
        else:
            return "Just now"
    
    @staticmethod
    def create_payment_submission_notification(payment_submission):
        """Create notification for new payment submission"""
        notification = Notification(
            title="New Payment Submission",
            message=f"{payment_submission.client.full_name} submitted a payment of ₦{payment_submission.amount:,.2f} for {payment_submission.get_item_name()}",
            notification_type="info",
            related_type="payment_submission",
            related_id=payment_submission.id
        )
        db.session.add(notification)
        db.session.commit()
        return notification
    
    @staticmethod
    def get_unread_count_for_staff():
        """Get count of unread notifications for staff"""
        return Notification.query.filter_by(
            user_id=None,  # Staff notifications
            is_read=False
        ).count()
    
    @staticmethod
    def get_recent_for_staff(limit=10):
        """Get recent notifications for staff"""
        return Notification.query.filter_by(
            user_id=None
        ).order_by(Notification.created_at.desc()).limit(limit).all()
