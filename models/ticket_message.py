from models.database import db
from datetime import datetime

class TicketMessage(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    ticket_id = db.<PERSON>umn(db.<PERSON><PERSON><PERSON>, db.<PERSON><PERSON><PERSON>('support_ticket.id'), nullable=False)
    user_id = db.Column(db.Integer, db.<PERSON>('user.id'), nullable=False)
    message = db.Column(db.Text, nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    # Relationships
    ticket = db.relationship('SupportTicket', backref=db.backref('messages', lazy=True))
    user = db.relationship('User', backref=db.backref('ticket_messages', lazy=True))
    
    def __repr__(self):
        return f"<TicketMessage {self.id} for Ticket {self.ticket_id}>"