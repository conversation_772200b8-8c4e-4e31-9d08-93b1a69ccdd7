from models.database import db
from datetime import datetime

class ClientService(db.Model):
    id = db.<PERSON>umn(db.Integer, primary_key=True)
    client_id = db.<PERSON>umn(db.Integer, db.<PERSON><PERSON><PERSON>('user.id'), nullable=False)
    service_id = db.Column(db.Inte<PERSON>, db.<PERSON>ey('service.id'), nullable=False)
    status = db.Column(db.String(50), default='Active')  # Active, Inactive, Completed
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationships
    client = db.relationship('User', backref=db.backref('services', lazy=True))
    service = db.relationship('Service', backref=db.backref('clients', lazy=True))
    
    def __repr__(self):
        return f"<ClientService {self.client_id}-{self.service_id}>"