from models.database import db
from datetime import datetime, date
from dateutil.relativedelta import relativedelta
from sqlalchemy.ext.declarative import declared_attr
from sqlalchemy.orm import foreign, relationship

class Property(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(200), nullable=False)
    property_type = db.Column(db.String(50), nullable=False)  # Commercial, Residential, etc.
    location = db.Column(db.String(500), nullable=False)
    price = db.Column(db.Float, nullable=False)
    description = db.Column(db.Text)
    status = db.Column(db.String(50), default='Available')  # Available, Sold, Rented, etc.
    payment_cadence = db.Column(db.String(20), default='one_time')  # one_time, monthly, quarterly, half_year, annual, biennial
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationships
    client_properties = relationship("ClientProperty", back_populates="property")
    clients = relationship("User", secondary="client_property", viewonly=True)
    payments = relationship("Payment", 
                          primaryjoin="and_(Payment.item_type=='property', Payment.item_id==Property.id)",
                          foreign_keys="[Payment.item_id]",
                          viewonly=True)
    
    def __repr__(self):
        return f"<Property {self.name}>"
        
    def is_payment_up_to_date(self):
        """Check if payments are up to date based on the payment cadence"""
        today = date.today()
        
        if not self.payments:
            return False
        
        # Sort payments by date, most recent first
        recent_payments = sorted(self.payments, key=lambda p: p.payment_date, reverse=True)
        latest_payment = recent_payments[0] if recent_payments else None
        
        if not latest_payment:
            return False
        
        if self.payment_cadence == 'one_time':
            # For one-time payments, any payment means it's up to date
            return True
        
        elif self.payment_cadence == 'monthly':
            # Check if payment was made in the current month
            return (latest_payment.payment_date.year == today.year and 
                    latest_payment.payment_date.month == today.month)
        
        elif self.payment_cadence == 'quarterly':
            # Determine current quarter
            current_quarter = (today.month - 1) // 3 + 1
            payment_quarter = (latest_payment.payment_date.month - 1) // 3 + 1
            
            return (latest_payment.payment_date.year == today.year and 
                    payment_quarter == current_quarter)
        
        elif self.payment_cadence == 'half_year':
            # Determine current half-year
            current_half = 1 if today.month <= 6 else 2
            payment_half = 1 if latest_payment.payment_date.month <= 6 else 2
            
            return (latest_payment.payment_date.year == today.year and 
                    payment_half == current_half)
        
        elif self.payment_cadence == 'annual':
            # Check if payment was made in the current year
            return latest_payment.payment_date.year == today.year
        
        elif self.payment_cadence == 'biennial':
            # Check if payment was made within the last 24 months
            two_years_ago = today - relativedelta(years=2)
            return latest_payment.payment_date >= two_years_ago
        
        return False

    def get_overdue_months(self):
        """Calculate how many months a payment is overdue"""
        today = date.today()
        
        if not self.payments:
            # If never paid, calculate from creation date
            months_since_creation = (today.year - self.created_at.year) * 12 + today.month - self.created_at.month
            return months_since_creation
        
        # Sort payments by date, most recent first
        recent_payments = sorted(self.payments, key=lambda p: p.payment_date, reverse=True)
        latest_payment = recent_payments[0]
        
        if self.is_payment_up_to_date():
            return 0
        
        if self.payment_cadence == 'one_time':
            # For one-time payments, not applicable
            return 0
        
        elif self.payment_cadence == 'monthly':
            # Calculate months since last payment
            months_since_payment = (today.year - latest_payment.payment_date.year) * 12 + today.month - latest_payment.payment_date.month
            return months_since_payment
        
        elif self.payment_cadence == 'quarterly':
            # Calculate quarters since last payment
            quarters_since_payment = ((today.year - latest_payment.payment_date.year) * 4 + 
                                     (today.month - 1) // 3 - 
                                     (latest_payment.payment_date.month - 1) // 3)
            return quarters_since_payment * 3  # Convert quarters to months
        
        elif self.payment_cadence == 'half_year':
            # Calculate half-years since last payment
            half_years_since_payment = ((today.year - latest_payment.payment_date.year) * 2 + 
                                       (1 if today.month > 6 else 0) - 
                                       (1 if latest_payment.payment_date.month > 6 else 0))
            return half_years_since_payment * 6  # Convert half-years to months
        
        elif self.payment_cadence == 'annual':
            # Calculate years since last payment
            years_since_payment = today.year - latest_payment.payment_date.year
            if today.month < latest_payment.payment_date.month:
                years_since_payment -= 1
            return years_since_payment * 12  # Convert years to months
        
        elif self.payment_cadence == 'biennial':
            # Calculate biennial periods since last payment
            years_since_payment = today.year - latest_payment.payment_date.year
            if today.month < latest_payment.payment_date.month:
                years_since_payment -= 1
            biennial_periods = years_since_payment // 2
            return biennial_periods * 24  # Convert biennial periods to months
        
        return 0

    def get_payment_status(self):
        """Get the payment status (Up to Date, Overdue, or Never Paid)"""
        if not self.payments:
            return "Never Paid"
        
        if self.is_payment_up_to_date():
            return "Up to Date"
        
        overdue_months = self.get_overdue_months()
        if overdue_months > 0:
            return f"Overdue ({overdue_months} month{'s' if overdue_months > 1 else ''})"
        
        return "Unknown"

    def get_amount_due(self):
        """Calculate the amount due for overdue payments"""
        today = date.today()
        
        # Get total amount paid for this property
        from models.payment import Payment
        total_paid = db.session.query(db.func.sum(Payment.amount)).filter(
            Payment.item_type == 'property',
            Payment.item_id == self.id
        ).scalar() or 0
        
        if self.payment_cadence == 'one_time':
            # For one-time payments, the amount due is the remaining balance
            return max(0, self.price - total_paid)
        
        # For recurring payments, calculate based on client assignment date
        from models.client_property import ClientProperty
        client_property = ClientProperty.query.filter_by(property_id=self.id).first()
        
        if not client_property:
            return 0  # No client assigned yet
        
        assignment_date = client_property.created_at.date()
        
        # If never paid, calculate based on time since assignment
        if not self.payments:
            months_since_assignment = (today.year - assignment_date.year) * 12 + today.month - assignment_date.month
            
            if months_since_assignment <= 0:
                return 0  # Not due yet
            
            if self.payment_cadence == 'monthly':
                return self.price * months_since_assignment
            elif self.payment_cadence == 'quarterly':
                quarters = (months_since_assignment + 2) // 3  # Round up
                return self.price * quarters
            elif self.payment_cadence == 'half_year':
                half_years = (months_since_assignment + 5) // 6  # Round up
                return self.price * half_years
            elif self.payment_cadence == 'annual':
                years = (months_since_assignment + 11) // 12  # Round up
                return self.price * years
            elif self.payment_cadence == 'biennial':
                biennial_periods = (months_since_assignment + 23) // 24  # Round up
                return self.price * biennial_periods
        
        # If payments exist, calculate based on last payment date
        recent_payments = sorted(self.payments, key=lambda p: p.payment_date, reverse=True)
        latest_payment = recent_payments[0]
        
        months_since_payment = (today.year - latest_payment.payment_date.year) * 12 + today.month - latest_payment.payment_date.month
        
        if months_since_payment <= 0:
            return 0  # Not due yet
        
        # Calculate total expected payments since assignment
        total_expected = 0
        
        if self.payment_cadence == 'monthly':
            total_expected = self.price * months_since_payment
        elif self.payment_cadence == 'quarterly':
            quarters = (months_since_payment + 2) // 3  # Round up
            total_expected = self.price * quarters
        elif self.payment_cadence == 'half_year':
            half_years = (months_since_payment + 5) // 6  # Round up
            total_expected = self.price * half_years
        elif self.payment_cadence == 'annual':
            years = (months_since_payment + 11) // 12  # Round up
            total_expected = self.price * years
        elif self.payment_cadence == 'biennial':
            biennial_periods = (months_since_payment + 23) // 24  # Round up
            total_expected = self.price * biennial_periods
        
        # Return the difference between expected and actual payments
        return max(0, total_expected - total_paid)
