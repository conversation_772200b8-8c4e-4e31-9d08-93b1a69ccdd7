from models.database import db
from datetime import datetime

class PaymentSubmission(db.Model):
    """Model for client payment submissions with proof attachments"""
    __tablename__ = 'payment_submissions'
    
    id = db.Column(db.Integer, primary_key=True)
    
    # Client information
    client_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    
    # Payment details
    item_type = db.Column(db.String(20), nullable=False)  # 'property' or 'service'
    item_id = db.Column(db.Integer, nullable=False)  # ID of property or service
    amount = db.Column(db.Numeric(10, 2), nullable=False)
    payment_date = db.Column(db.Date, nullable=False)
    payment_method = db.Column(db.String(50), nullable=False)  # 'bank_transfer', 'cash', 'check', etc.
    reference_number = db.Column(db.String(100))  # Transaction reference or check number
    
    # Proof attachment
    proof_filename = db.Column(db.String(255))  # Original filename
    proof_filepath = db.Column(db.String(500))  # Path to stored file
    proof_mimetype = db.Column(db.String(100))  # File MIME type
    
    # Additional details
    description = db.Column(db.Text)  # Optional description from client
    
    # Status tracking
    status = db.Column(db.String(20), default='pending')  # 'pending', 'approved', 'rejected'
    reviewed_by = db.Column(db.Integer, db.ForeignKey('user.id'))  # Staff member who reviewed
    reviewed_at = db.Column(db.DateTime)
    review_notes = db.Column(db.Text)  # Staff notes during review
    
    # Timestamps
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationships
    client = db.relationship('User', foreign_keys=[client_id], backref='payment_submissions')
    reviewer = db.relationship('User', foreign_keys=[reviewed_by], post_update=True)
    
    def __repr__(self):
        return f'<PaymentSubmission {self.id}: {self.client.full_name} - ${self.amount}>'
    
    def get_item_name(self):
        """Get the name of the related property or service"""
        if self.item_type == 'property':
            from models.property import Property
            item = Property.query.get(self.item_id)
            return item.name if item else 'Unknown Property'
        elif self.item_type == 'service':
            from models.service import Service
            item = Service.query.get(self.item_id)
            return item.name if item else 'Unknown Service'
        return 'Unknown Item'
    
    def get_item_details(self):
        """Get the full details of the related property or service"""
        if self.item_type == 'property':
            from models.property import Property
            return Property.query.get(self.item_id)
        elif self.item_type == 'service':
            from models.service import Service
            return Service.query.get(self.item_id)
        return None
    
    def get_status_badge_class(self):
        """Get CSS class for status badge"""
        status_classes = {
            'pending': 'bg-yellow-100 text-yellow-800',
            'approved': 'bg-green-100 text-green-800',
            'rejected': 'bg-red-100 text-red-800'
        }
        return status_classes.get(self.status, 'bg-gray-100 text-gray-800')
    
    def approve(self, reviewer_id, notes=None):
        """Approve the payment submission"""
        self.status = 'approved'
        self.reviewed_by = reviewer_id
        self.reviewed_at = datetime.utcnow()
        if notes:
            self.review_notes = notes
        db.session.commit()
    
    def reject(self, reviewer_id, notes=None):
        """Reject the payment submission"""
        self.status = 'rejected'
        self.reviewed_by = reviewer_id
        self.reviewed_at = datetime.utcnow()
        if notes:
            self.review_notes = notes
        db.session.commit()
    
    def has_proof_attachment(self):
        """Check if submission has a proof attachment"""
        return bool(self.proof_filename and self.proof_filepath)
    
    def get_proof_file_extension(self):
        """Get file extension of proof attachment"""
        if self.proof_filename:
            return self.proof_filename.rsplit('.', 1)[1].lower() if '.' in self.proof_filename else ''
        return ''
    
    def is_image_proof(self):
        """Check if proof attachment is an image"""
        if self.proof_mimetype:
            return self.proof_mimetype.startswith('image/')
        return False
    
    def is_pdf_proof(self):
        """Check if proof attachment is a PDF"""
        return self.proof_mimetype == 'application/pdf'
