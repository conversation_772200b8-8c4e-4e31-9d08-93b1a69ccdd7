from models.database import db
from datetime import datetime
from sqlalchemy.orm import relationship

class ClientProperty(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    client_id = db.Column(db.<PERSON>te<PERSON>, db.<PERSON><PERSON><PERSON>('user.id'), nullable=False)
    property_id = db.Column(db.Integer, db.<PERSON>ey('property.id'), nullable=False)
    status = db.Column(db.String(50), default='Active')  # Active, Inactive, Completed
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationships
    client = relationship("User", back_populates="client_properties")
    property = relationship("Property", back_populates="client_properties")
    
    def __repr__(self):
        return f"<ClientProperty {self.client_id}-{self.property_id}>"
