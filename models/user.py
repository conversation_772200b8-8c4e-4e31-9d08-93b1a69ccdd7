from models.database import db
import bcrypt
from flask import session
from datetime import datetime
from sqlalchemy.orm import relationship

class User(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    email = db.Column(db.String(120), unique=True, nullable=False)
    password_hash = db.Column(db.String(256))
    firstname = db.Column(db.String(100))
    lastname = db.Column(db.String(100))
    address = db.Column(db.String(200))
    phone = db.Column(db.String(20))
    role = db.Column(db.String(20))  # 'super_user', 'staff', 'client'
    active = db.Column(db.<PERSON><PERSON>, default=True)
    designation = db.Column(db.String(100))
    department = db.Column(db.String(100))
    
    # Add created_at and updated_at fields
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationship with payments is defined in the Payment model
    
    # Add relationships
    client_properties = relationship("ClientProperty", back_populates="client")
    properties = relationship("Property", secondary="client_property", viewonly=True)
    
    def set_password(self, password):
        """Hash password using bcrypt with salt"""
        if isinstance(password, str):
            password = password.encode('utf-8')

        # Generate salt and hash password
        salt = bcrypt.gensalt(rounds=12)  # 12 rounds for good security/performance balance
        self.password_hash = bcrypt.hashpw(password, salt).decode('utf-8')

    def check_password(self, password):
        """Verify password against bcrypt hash"""
        if not self.password_hash:
            return False

        if isinstance(password, str):
            password = password.encode('utf-8')

        if isinstance(self.password_hash, str):
            stored_hash = self.password_hash.encode('utf-8')
        else:
            stored_hash = self.password_hash

        try:
            return bcrypt.checkpw(password, stored_hash)
        except (ValueError, TypeError):
            return False
    
    def has_password(self):
        return self.password_hash is not None

    def is_bcrypt_hash(self):
        """Check if the stored hash is a bcrypt hash"""
        return self.password_hash and self.password_hash.startswith('$2b$')

    def migrate_password_to_bcrypt(self, password):
        """
        Migrate from Werkzeug hash to bcrypt hash during login
        This allows seamless transition from old password system
        """
        from werkzeug.security import check_password_hash

        # Check if current hash is Werkzeug format and password is correct
        if not self.is_bcrypt_hash() and check_password_hash(self.password_hash, password):
            # Re-hash with bcrypt
            self.set_password(password)
            return True
        return False

    def check_password_with_migration(self, password):
        """
        Check password and automatically migrate from Werkzeug to bcrypt if needed
        """
        # If already bcrypt, use normal check
        if self.is_bcrypt_hash():
            return self.check_password(password)

        # Try to migrate from Werkzeug hash
        if self.migrate_password_to_bcrypt(password):
            return True

        return False

    def get_password_strength_info(self):
        """Get information about password hash strength"""
        if not self.password_hash:
            return {'type': 'none', 'strength': 'No password set'}

        if self.is_bcrypt_hash():
            # Extract rounds from bcrypt hash
            try:
                rounds = int(self.password_hash.split('$')[2])
                if rounds >= 12:
                    strength = 'Strong'
                elif rounds >= 10:
                    strength = 'Good'
                else:
                    strength = 'Weak'
                return {
                    'type': 'bcrypt',
                    'rounds': rounds,
                    'strength': strength
                }
            except (IndexError, ValueError):
                return {'type': 'bcrypt', 'strength': 'Unknown'}
        else:
            return {'type': 'legacy', 'strength': 'Legacy (needs upgrade)'}
        
    @property
    def full_name(self):
        return f"{self.firstname} {self.lastname}"
    
    # Role helper properties
    @property
    def is_super_user(self):
        return self.role == 'super_user'
    
    @property
    def is_staff(self):
        return self.role == 'staff'
    
    @property
    def is_client(self):
        return self.role == 'client'
    
    @property
    def is_active(self):
        return self.active

    @property
    def first_name(self):
        return self.firstname

    @property
    def last_name(self):
        return self.lastname
