from models.database import db
from werkzeug.security import generate_password_hash, check_password_hash
from flask import session
from datetime import datetime
from sqlalchemy.orm import relationship

class User(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    email = db.Column(db.String(120), unique=True, nullable=False)
    password_hash = db.Column(db.String(256))
    firstname = db.Column(db.String(100))
    lastname = db.Column(db.String(100))
    address = db.Column(db.String(200))
    phone = db.Column(db.String(20))
    role = db.Column(db.String(20))  # 'super_user', 'staff', 'client'
    active = db.Column(db.<PERSON>, default=True)
    designation = db.Column(db.String(100))
    department = db.Column(db.String(100))
    
    # Add created_at and updated_at fields
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationship with payments is defined in the Payment model
    
    # Add relationships
    client_properties = relationship("ClientProperty", back_populates="client")
    properties = relationship("Property", secondary="client_property", viewonly=True)
    
    def set_password(self, password):
        self.password_hash = generate_password_hash(password)
        
    def check_password(self, password):
        return check_password_hash(self.password_hash, password)
    
    def has_password(self):
        return self.password_hash is not None
        
    @property
    def full_name(self):
        return f"{self.firstname} {self.lastname}"
    
    # Role helper properties
    @property
    def is_super_user(self):
        return self.role == 'super_user'
    
    @property
    def is_staff(self):
        return self.role == 'staff'
    
    @property
    def is_client(self):
        return self.role == 'client'
    
    @property
    def is_active(self):
        return self.active

    @property
    def first_name(self):
        return self.firstname

    @property
    def last_name(self):
        return self.lastname
