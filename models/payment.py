from models.database import db
from datetime import datetime, date, timedelta
from sqlalchemy.ext.hybrid import hybrid_property

class Payment(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    client_id = db.Column(db.<PERSON><PERSON>ger, db.<PERSON><PERSON>('user.id'), nullable=False)
    item_type = db.Column(db.String(50), nullable=False)  # 'property' or 'service'
    item_id = db.Column(db.Integer, nullable=False)
    amount = db.Column(db.Float, nullable=False)
    payment_date = db.Column(db.Date, nullable=False)
    status = db.Column(db.String(50), default='Completed')  # Completed, Pending, Failed
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationship with client
    client = db.relationship('User', backref='payments')
    
    # Dynamic property relationships
    @hybrid_property
    def related_item(self):
        if self.item_type == 'property':
            from models.property import Property
            return Property.query.get(self.item_id)
        elif self.item_type == 'service':
            from models.service import Service
            return Service.query.get(self.item_id)
        return None
    
    @hybrid_property
    def calculated_status(self):
        """Automatically calculate payment status based on amount due and due dates"""
        if self.related_item is None:
            return "Unknown"
        
        # Get the total price of the item
        item_price = getattr(self.related_item, 'price', 0)
        
        # Get total amount paid for this item
        total_paid = db.session.query(db.func.sum(Payment.amount)).filter(
            Payment.item_type == self.item_type,
            Payment.item_id == self.item_id
        ).scalar() or 0
        
        # Check if this is a partial payment
        if self.amount < item_price:
            return "Partial"
        
        # For one-time payments, check if the full amount has been paid
        if hasattr(self.related_item, 'payment_cadence') and self.related_item.payment_cadence == 'one_time':
            if total_paid >= item_price:
                return "Completed"
            elif total_paid > 0:
                return "Partial"
            else:
                return "Overdue"
        
        # Get amount due for the related item
        amount_due = self.related_item.get_amount_due()
        
        # For recurring payments
        if amount_due > 0:
            # Check if partial payment was made but still has amount due
            if total_paid > 0 and total_paid < item_price:
                return "Partial"
            
            # Check if the item has a payment cadence
            if hasattr(self.related_item, 'payment_cadence'):
                # For properties with payment cadence
                if self.related_item.payment_cadence != 'one_time':
                    # Calculate next due date based on the most recent payment
                    today = date.today()
                    
                    # Get all payments for this item
                    all_payments = Payment.query.filter_by(
                        item_type=self.item_type,
                        item_id=self.item_id
                    ).order_by(Payment.payment_date.desc()).all()
                    
                    if not all_payments:
                        return "Overdue"
                        
                    latest_payment = all_payments[0]
                    next_due_date = None
                    
                    # Calculate next due date based on payment cadence
                    if self.related_item.payment_cadence == 'monthly':
                        # Next payment due one month after the latest payment
                        next_month = latest_payment.payment_date.month + 1
                        next_year = latest_payment.payment_date.year
                        if next_month > 12:
                            next_month = 1
                            next_year += 1
                        next_due_date = date(next_year, next_month, latest_payment.payment_date.day)
                    
                    elif self.related_item.payment_cadence == 'quarterly':
                        # Next payment due three months after the latest payment
                        next_month = latest_payment.payment_date.month + 3
                        next_year = latest_payment.payment_date.year
                        while next_month > 12:
                            next_month -= 12
                            next_year += 1
                        next_due_date = date(next_year, next_month, latest_payment.payment_date.day)
                    
                    elif self.related_item.payment_cadence == 'half_year':
                        # Next payment due six months after the latest payment
                        next_month = latest_payment.payment_date.month + 6
                        next_year = latest_payment.payment_date.year
                        while next_month > 12:
                            next_month -= 12
                            next_year += 1
                        next_due_date = date(next_year, next_month, latest_payment.payment_date.day)
                    
                    elif self.related_item.payment_cadence == 'annual':
                        # Next payment due one year after the latest payment
                        next_due_date = date(
                            latest_payment.payment_date.year + 1,
                            latest_payment.payment_date.month,
                            latest_payment.payment_date.day
                        )
                    
                    elif self.related_item.payment_cadence == 'biennial':
                        # Next payment due two years after the latest payment
                        next_due_date = date(
                            latest_payment.payment_date.year + 2,
                            latest_payment.payment_date.month,
                            latest_payment.payment_date.day
                        )
                    
                    # If we have a next due date, check if it's within 30 days
                    if next_due_date:
                        if next_due_date < today:
                            return "Overdue"
                        elif (next_due_date - today).days <= 30:
                            return "Pending"
        
        # Default to overdue if there's an amount due
        if amount_due > 0:
            return "Overdue"
            
        return "Completed"
    
    def update_status(self):
        """Update the payment status based on calculated_status"""
        self.status = self.calculated_status
        return self.status
    
    def __repr__(self):
        return f"<Payment {self.id} - {self.amount}>"
