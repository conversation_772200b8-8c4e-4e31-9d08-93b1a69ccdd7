from models.database import db
from datetime import datetime
from sqlalchemy.ext.declarative import declared_attr
from sqlalchemy.orm import foreign

class Service(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(200), nullable=False)
    service_type = db.Column(db.String(50), nullable=False)  # Construction, Consulting, etc.
    description = db.Column(db.Text)
    price = db.Column(db.Float, nullable=False)
    status = db.Column(db.String(50), default='Active')  # Active, Inactive
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationships
    @declared_attr
    def payments(cls):
        from models.payment import Payment
        return db.relationship(
            Payment,
            primaryjoin=db.and_(
                Payment.item_type == 'service',
                foreign(Payment.item_id) == cls.id
            ),
            viewonly=True,
            overlaps="service"
        )
    
    def __repr__(self):
        return f"<Service {self.name}>"

    def is_payment_up_to_date(self):
        """Check if payments are up to date"""
        # For services, typically just check if any payment has been made
        return len(self.payments) > 0

    def get_payment_status(self):
        """Get the payment status (Up to Date, Overdue, or Never Paid)"""
        if not self.payments:
            return "Never Paid"
        
        if self.is_payment_up_to_date():
            return "Up to Date"
        
        return "Overdue"

    def get_amount_due(self):
        """Calculate the amount due for the service"""
        # Get total amount paid for this service
        from models.payment import Payment
        total_paid = db.session.query(db.func.sum(Payment.amount)).filter(
            Payment.item_type == 'service',
            Payment.item_id == self.id
        ).scalar() or 0
        
        # For services, return the remaining balance
        return max(0, self.price - total_paid)
