#!/usr/bin/env python3
"""
Test script to verify the dashboard calculations are working correctly
"""

from flask import Flask
from models.database import db
from models.user import User
from models.property import Property
from models.service import Service
from models.payment import Payment
from models.client_property import ClientProperty
from models.client_service import ClientService

def create_app():
    """Create Flask app with database configuration"""
    app = Flask(__name__)
    app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///greenpeak.db'
    app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
    db.init_app(app)
    return app

def test_dashboard_calculations():
    """Test the dashboard calculations"""
    app = create_app()
    
    with app.app_context():
        print("=== Testing Dashboard Calculations ===\n")
        
        # Get all properties and services
        properties = Property.query.all()
        services = Service.query.all()
        
        print("--- Property Analysis ---")
        total_paid_properties = 0
        total_owed_properties = 0
        
        for prop in properties:
            paid = prop.get_total_paid()
            owed = prop.get_corrected_amount_due()
            total_paid_properties += paid
            total_owed_properties += owed
            
            print(f"Property: {prop.name}")
            print(f"  Annual Price: ${prop.price:,.2f}")
            print(f"  Payment Cadence: {prop.payment_cadence}")
            print(f"  Total Paid: ${paid:,.2f}")
            print(f"  Amount Owed: ${owed:,.2f}")
            print(f"  Status: {'✅ Up to Date' if owed == 0 else '❌ Owes Money'}")
            print()
        
        print("--- Service Analysis ---")
        total_paid_services = 0
        total_owed_services = 0
        
        for service in services:
            paid = service.get_total_paid()
            owed = service.get_amount_due()
            total_paid_services += paid
            total_owed_services += owed
            
            print(f"Service: {service.name}")
            print(f"  Price: ${service.price:,.2f}")
            print(f"  Total Paid: ${paid:,.2f}")
            print(f"  Amount Owed: ${owed:,.2f}")
            print(f"  Status: {'✅ Up to Date' if owed == 0 else '❌ Owes Money'}")
            print()
        
        # Calculate totals
        total_amount_paid = total_paid_properties + total_paid_services
        total_amount_owed = total_owed_properties + total_owed_services
        
        print("--- Dashboard Summary ---")
        print(f"Properties:")
        print(f"  Total Paid: ${total_paid_properties:,.2f}")
        print(f"  Total Owed: ${total_owed_properties:,.2f}")
        print()
        print(f"Services:")
        print(f"  Total Paid: ${total_paid_services:,.2f}")
        print(f"  Total Owed: ${total_owed_services:,.2f}")
        print()
        print(f"GRAND TOTALS:")
        print(f"  Total Amount Paid: ${total_amount_paid:,.2f}")
        print(f"  Total Amount Owed: ${total_amount_owed:,.2f}")
        print()
        
        # Verify Emma's specific debt
        print("--- Emma's Debt Verification ---")
        emma = User.query.filter_by(email='<EMAIL>').first()
        if emma:
            emma_properties = ClientProperty.query.filter_by(client_id=emma.id).all()
            emma_total_owed = 0
            
            for cp in emma_properties:
                prop = Property.query.get(cp.property_id)
                if prop:
                    owed = prop.get_corrected_amount_due()
                    emma_total_owed += owed
                    print(f"  {prop.name}: ${owed:,.2f}")
            
            print(f"Emma's Total Debt: ${emma_total_owed:,.2f}")
            print(f"Expected: $2,730,000.00")
            print(f"Match: {'✅ YES' if abs(emma_total_owed - 2730000) < 1 else '❌ NO'}")

if __name__ == "__main__":
    test_dashboard_calculations()
