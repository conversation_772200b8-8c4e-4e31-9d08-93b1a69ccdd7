from app import app, db
from models.user import User
from werkzeug.security import generate_password_hash, check_password_hash
import os

def debug_database():
    with app.app_context():
        print("\n=== DATABASE DIAGNOSTICS ===")
        
        # Check if database file exists
        db_path = app.config['SQLALCHEMY_DATABASE_URI'].replace('sqlite:///', '')
        print(f"Database path: {db_path}")
        print(f"Database file exists: {os.path.exists(db_path)}")
        
        # Check tables
        try:
            tables = db.engine.table_names()
            print(f"Tables in database: {tables}")
            
            # Check if user table exists
            if 'user' not in tables:
                print("ERROR: 'user' table does not exist!")
                return
        except Exception as e:
            print(f"Error checking tables: {e}")
            return
        
        # Check user table structure
        try:
            result = db.session.execute(db.text("PRAGMA table_info(user)"))
            columns = result.fetchall()
            print("\nUser table columns:")
            for col in columns:
                print(f"  {col}")
        except Exception as e:
            print(f"Error checking user table structure: {e}")
        
        # List all users
        try:
            users = User.query.all()
            print(f"\nTotal users in database: {len(users)}")
            
            if not users:
                print("No users found in the database!")
                # Create a test user
                create_test_user()
                return
            
            for user in users:
                print(f"\nUser ID: {user.id}")
                print(f"Email: {user.email}")
                print(f"Role: {user.role}")
                print(f"Active: {user.active}")
                print(f"Has password: {user.has_password()}")
                print(f"Password hash: {user.password_hash[:20]}..." if user.password_hash else "None")
                
                # Test password verification
                if user.password_hash:
                    test_password = "Gr33nP3@k5hel+3rs2025"  # Default test password
                    print(f"Testing password verification with '{test_password}':")
                    print(f"  Direct check_password_hash: {check_password_hash(user.password_hash, test_password)}")
                    print(f"  User.check_password: {user.check_password(test_password)}")
        except Exception as e:
            print(f"Error listing users: {e}")

def create_test_user():
    with app.app_context():
        print("\n=== CREATING TEST USER ===")
        try:
            # Create a test admin user
            test_user = User(
                email="<EMAIL>",
                firstname="Test",
                lastname="User",
                role="super_user",
                active=True
            )
            
            # Set password directly
            password = "password123"
            test_user.password_hash = generate_password_hash(password)
            
            # Add to database
            db.session.add(test_user)
            db.session.commit()
            
            print(f"Test user created with email: {test_user.email} and password: {password}")
            
            # Verify the user was created
            user = User.query.filter_by(email="<EMAIL>").first()
            if user:
                print(f"User found in database: {user.email}")
                print(f"Password verification test: {user.check_password(password)}")
            else:
                print("ERROR: User not found after creation!")
        except Exception as e:
            print(f"Error creating test user: {e}")
            db.session.rollback()

if __name__ == "__main__":
    debug_database()