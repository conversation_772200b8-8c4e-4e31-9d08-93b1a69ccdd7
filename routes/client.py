
from flask import Blueprint, render_template, g, request, flash, redirect, url_for
from utils.auth import role_required
from models.database import db

client_bp = Blueprint('client', __name__, url_prefix='/client')

@client_bp.route('/dashboard')
@role_required(['client'])
def dashboard():
    # Get the current user's properties and services data
    from models.client_property import ClientProperty
    from models.property import Property
    from models.client_service import ClientService
    from models.service import Service
    from models.payment import Payment
    from flask import g

    # Count properties and services
    property_count = ClientProperty.query.filter_by(client_id=g.user.id).count()
    service_count = ClientService.query.filter_by(client_id=g.user.id).count()

    # Sum total payments made by this client
    total_payments = db.session.query(db.func.sum(Payment.amount)).filter_by(client_id=g.user.id).scalar() or 0

    # Calculate amounts paid and owed for properties
    client_properties = ClientProperty.query.filter_by(client_id=g.user.id).all()
    total_paid_properties = 0
    total_owed_properties = 0

    for cp in client_properties:
        property_obj = Property.query.get(cp.property_id)
        if property_obj:
            total_paid_properties += property_obj.get_total_paid()
            total_owed_properties += property_obj.get_corrected_amount_due()

    # Calculate amounts paid and owed for services
    client_services = ClientService.query.filter_by(client_id=g.user.id).all()
    total_paid_services = 0
    total_owed_services = 0

    for cs in client_services:
        service_obj = Service.query.get(cs.service_id)
        if service_obj:
            total_paid_services += service_obj.get_total_paid()
            total_owed_services += service_obj.get_amount_due()

    # Calculate totals
    total_amount_paid = total_paid_properties + total_paid_services
    total_amount_owed = total_owed_properties + total_owed_services

    return render_template('client/dashboard.html',
                          property_count=property_count,
                          service_count=service_count,
                          total_payments="{:,.2f}".format(total_payments),
                          total_amount_paid=total_amount_paid,
                          total_amount_owed=total_amount_owed,
                          total_paid_properties=total_paid_properties,
                          total_owed_properties=total_owed_properties,
                          total_paid_services=total_paid_services,
                          total_owed_services=total_owed_services)

@client_bp.route('/properties')
@role_required(['client'])
def property_list():
    # Get the current user's properties through the ClientProperty relationship
    from models.client_property import ClientProperty
    from models.property import Property
    from flask import g
    
    client_properties = ClientProperty.query.filter_by(client_id=g.user.id).all()
    properties = [cp.property for cp in client_properties]
    
    return render_template('client/property_list.html', properties=properties)

@client_bp.route('/properties/<int:property_id>')
@role_required(['client'])
def property_detail(property_id):
    # Get the property and verify the client has access to it
    from models.client_property import ClientProperty
    from models.property import Property
    from models.payment import Payment
    from flask import g

    # Check if this client has access to this property
    client_property = ClientProperty.query.filter_by(
        client_id=g.user.id,
        property_id=property_id
    ).first_or_404()

    # Get the property details
    property_obj = Property.query.get_or_404(property_id)

    # Get payment history for this property by this client
    payments = Payment.query.filter_by(
        client_id=g.user.id,
        item_type='property',
        item_id=property_id
    ).order_by(Payment.payment_date.desc()).all()

    # Calculate payment analytics for this property
    total_paid = property_obj.get_total_paid()
    amount_owed = property_obj.get_corrected_amount_due()

    return render_template('client/property_detail.html',
                          property=property_obj,
                          client_property=client_property,
                          payments=payments,
                          total_paid=total_paid,
                          amount_owed=amount_owed)

@client_bp.route('/services')
@role_required(['client'])
def service_list():
    # Get the current user's services through the ClientService relationship
    from models.client_service import ClientService
    from models.service import Service
    from flask import g
    
    client_services = ClientService.query.filter_by(client_id=g.user.id).all()
    services = [cs.service for cs in client_services]
    
    return render_template('client/service_list.html', services=services)

@client_bp.route('/payments')
@role_required(['client'])
def payment_list():
    # Get the current user's payment history
    from models.payment import Payment
    from flask import g
    
    payments = Payment.query.filter_by(client_id=g.user.id).order_by(Payment.payment_date.desc()).all()
    
    return render_template('client/payment_list.html', payments=payments)

@client_bp.route('/support', methods=['GET'])
@role_required(['client'])
def support():
    # Get current user's support tickets
    from models.support_ticket import SupportTicket
    from flask import g
    
    tickets = SupportTicket.query.filter_by(user_id=g.user.id).all()
    return render_template('client/support.html', tickets=tickets)

@client_bp.route('/support/create', methods=['GET', 'POST'])
@role_required(['client'])
def create_ticket():
    from models.support_ticket import SupportTicket
    from models.ticket_message import TicketMessage
    from models.database import db
    from flask import g
    
    if request.method == 'POST':
        subject = request.form.get('subject')
        message = request.form.get('message')
        
        if not subject or not message:
            flash('Subject and message are required', 'error')
            return render_template('client/create_ticket.html')
        
        new_ticket = SupportTicket(
            user_id=g.user.id,
            subject=subject,
            status='open'
        )
        
        db.session.add(new_ticket)
        db.session.commit()
        
        # Add initial message
        ticket_message = TicketMessage(
            ticket_id=new_ticket.id,
            user_id=g.user.id,
            message=message
        )
        
        db.session.add(ticket_message)
        db.session.commit()
        
        flash('Support ticket created successfully', 'success')
        return redirect(url_for('client.support'))
        
    return render_template('client/create_ticket.html')

@client_bp.route('/support/<int:ticket_id>', methods=['GET', 'POST'])
@role_required(['client'])
def view_ticket(ticket_id):
    from models.support_ticket import SupportTicket
    from models.ticket_message import TicketMessage
    from models.database import db
    from flask import g
    
    ticket = SupportTicket.query.filter_by(id=ticket_id, user_id=g.user.id).first_or_404()
    messages = TicketMessage.query.filter_by(ticket_id=ticket.id).order_by(TicketMessage.created_at).all()
    
    if request.method == 'POST':
        message = request.form.get('message')
        
        if not message:
            flash('Message cannot be empty', 'error')
            return redirect(url_for('client.view_ticket', ticket_id=ticket.id))
        
        new_message = TicketMessage(
            ticket_id=ticket.id,
            user_id=g.user.id,
            message=message
        )
        
        db.session.add(new_message)
        db.session.commit()
        
        flash('Reply added successfully', 'success')
        return redirect(url_for('client.view_ticket', ticket_id=ticket.id))
    
    return render_template('client/view_ticket.html', ticket=ticket, messages=messages)
