
from flask import Blueprint, render_template, g, request, flash, redirect, url_for
from utils.auth import role_required
from models.database import db

client_bp = Blueprint('client', __name__, url_prefix='/client')

@client_bp.route('/dashboard')
@role_required(['client'])
def dashboard():
    # Get the current user's properties and services data
    from models.client_property import ClientProperty
    from models.property import Property
    from models.client_service import ClientService
    from models.service import Service
    from models.payment import Payment
    from flask import g

    # Count properties and services
    property_count = ClientProperty.query.filter_by(client_id=g.user.id).count()
    service_count = ClientService.query.filter_by(client_id=g.user.id).count()

    # Sum total payments made by this client
    total_payments = db.session.query(db.func.sum(Payment.amount)).filter_by(client_id=g.user.id).scalar() or 0

    # Calculate amounts paid and owed for properties
    client_properties = ClientProperty.query.filter_by(client_id=g.user.id).all()
    total_paid_properties = 0
    total_owed_properties = 0

    for cp in client_properties:
        property_obj = Property.query.get(cp.property_id)
        if property_obj:
            total_paid_properties += property_obj.get_total_paid()
            total_owed_properties += property_obj.get_corrected_amount_due()

    # Calculate amounts paid and owed for services
    client_services = ClientService.query.filter_by(client_id=g.user.id).all()
    total_paid_services = 0
    total_owed_services = 0

    for cs in client_services:
        service_obj = Service.query.get(cs.service_id)
        if service_obj:
            total_paid_services += service_obj.get_total_paid()
            total_owed_services += service_obj.get_amount_due()

    # Calculate totals
    total_amount_paid = total_paid_properties + total_paid_services
    total_amount_owed = total_owed_properties + total_owed_services

    return render_template('client/dashboard.html',
                          property_count=property_count,
                          service_count=service_count,
                          total_payments="{:,.2f}".format(total_payments),
                          total_amount_paid=total_amount_paid,
                          total_amount_owed=total_amount_owed,
                          total_paid_properties=total_paid_properties,
                          total_owed_properties=total_owed_properties,
                          total_paid_services=total_paid_services,
                          total_owed_services=total_owed_services)

@client_bp.route('/properties')
@role_required(['client'])
def property_list():
    # Get the current user's properties through the ClientProperty relationship
    from models.client_property import ClientProperty
    from models.property import Property
    from flask import g
    
    client_properties = ClientProperty.query.filter_by(client_id=g.user.id).all()
    properties = [cp.property for cp in client_properties]
    
    return render_template('client/property_list.html', properties=properties)

@client_bp.route('/properties/<int:property_id>')
@role_required(['client'])
def property_detail(property_id):
    # Get the property and verify the client has access to it
    from models.client_property import ClientProperty
    from models.property import Property
    from models.payment import Payment
    from flask import g

    # Check if this client has access to this property
    client_property = ClientProperty.query.filter_by(
        client_id=g.user.id,
        property_id=property_id
    ).first_or_404()

    # Get the property details
    property_obj = Property.query.get_or_404(property_id)

    # Get payment history for this property by this client
    payments = Payment.query.filter_by(
        client_id=g.user.id,
        item_type='property',
        item_id=property_id
    ).order_by(Payment.payment_date.desc()).all()

    # Calculate payment analytics for this property
    total_paid = property_obj.get_total_paid()
    amount_owed = property_obj.get_corrected_amount_due()

    return render_template('client/property_detail.html',
                          property=property_obj,
                          client_property=client_property,
                          payments=payments,
                          total_paid=total_paid,
                          amount_owed=amount_owed)

@client_bp.route('/services')
@role_required(['client'])
def service_list():
    # Get the current user's services through the ClientService relationship
    from models.client_service import ClientService
    from models.service import Service
    from flask import g
    
    client_services = ClientService.query.filter_by(client_id=g.user.id).all()
    services = [cs.service for cs in client_services]
    
    return render_template('client/service_list.html', services=services)

@client_bp.route('/payments')
@role_required(['client'])
def payment_list():
    # Get the current user's payment history
    from models.payment import Payment
    from flask import g
    
    payments = Payment.query.filter_by(client_id=g.user.id).order_by(Payment.payment_date.desc()).all()
    
    return render_template('client/payment_list.html', payments=payments)

@client_bp.route('/payments/<int:payment_id>/receipt')
@role_required(['client'])
def payment_receipt(payment_id):
    # Get the payment and verify the client has access to it
    from models.payment import Payment
    from models.property import Property
    from models.service import Service
    from flask import g

    # Check if this client has access to this payment
    payment = Payment.query.filter_by(
        id=payment_id,
        client_id=g.user.id
    ).first_or_404()

    # Get the related item (property or service)
    if payment.item_type == 'property':
        related_item = Property.query.get(payment.item_id)
    else:
        related_item = Service.query.get(payment.item_id)

    from datetime import datetime

    return render_template('client/payment_receipt.html',
                          payment=payment,
                          related_item=related_item,
                          client=g.user,
                          current_date=datetime.now())

@client_bp.route('/payments/<int:payment_id>/receipt/pdf')
@role_required(['client'])
def payment_receipt_pdf(payment_id):
    # Get the payment and verify the client has access to it
    from models.payment import Payment
    from models.property import Property
    from models.service import Service
    from flask import g, make_response
    import io
    from datetime import datetime

    # Check if this client has access to this payment
    payment = Payment.query.filter_by(
        id=payment_id,
        client_id=g.user.id
    ).first_or_404()

    # Get the related item (property or service)
    if payment.item_type == 'property':
        related_item = Property.query.get(payment.item_id)
    else:
        related_item = Service.query.get(payment.item_id)

    try:
        from reportlab.lib.pagesizes import letter, A4
        from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle
        from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
        from reportlab.lib.units import inch
        from reportlab.lib import colors
        from reportlab.lib.enums import TA_CENTER, TA_LEFT, TA_RIGHT

        # Create a BytesIO buffer for the PDF
        buffer = io.BytesIO()

        # Create the PDF document
        doc = SimpleDocTemplate(buffer, pagesize=A4, topMargin=0.5*inch)
        styles = getSampleStyleSheet()

        # Custom styles
        title_style = ParagraphStyle(
            'CustomTitle',
            parent=styles['Heading1'],
            fontSize=24,
            spaceAfter=30,
            alignment=TA_CENTER,
            textColor=colors.HexColor('#1f2937')
        )

        header_style = ParagraphStyle(
            'CustomHeader',
            parent=styles['Heading2'],
            fontSize=16,
            spaceAfter=12,
            textColor=colors.HexColor('#059669')
        )

        # Build the PDF content
        story = []

        # Company Header
        story.append(Paragraph("GREEN PEAK SHELTERS", title_style))
        story.append(Paragraph("Payment Receipt", header_style))
        story.append(Spacer(1, 20))

        # Receipt Information Table
        receipt_data = [
            ['Receipt #:', f'GP-{payment.id:06d}'],
            ['Date Issued:', datetime.now().strftime('%B %d, %Y')],
            ['Payment Date:', payment.payment_date.strftime('%B %d, %Y')],
            ['Status:', payment.status],
        ]

        receipt_table = Table(receipt_data, colWidths=[2*inch, 3*inch])
        receipt_table.setStyle(TableStyle([
            ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
            ('FONTNAME', (0, 0), (0, -1), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, -1), 12),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 8),
        ]))
        story.append(receipt_table)
        story.append(Spacer(1, 20))

        # Client Information
        story.append(Paragraph("Bill To:", header_style))
        client_data = [
            ['Client Name:', g.user.full_name],
            ['Email:', g.user.email],
            ['Client ID:', f'GP-CLIENT-{g.user.id:04d}'],
        ]

        client_table = Table(client_data, colWidths=[2*inch, 4*inch])
        client_table.setStyle(TableStyle([
            ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
            ('FONTNAME', (0, 0), (0, -1), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, -1), 12),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 8),
        ]))
        story.append(client_table)
        story.append(Spacer(1, 20))

        # Payment Details
        story.append(Paragraph("Payment Details:", header_style))
        payment_data = [
            ['Description', 'Type', 'Amount'],
            [
                related_item.name if related_item else f'Unknown {payment.item_type}',
                payment.item_type.title(),
                f'₦{payment.amount:,.2f}'
            ]
        ]

        payment_table = Table(payment_data, colWidths=[3*inch, 1.5*inch, 1.5*inch])
        payment_table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), colors.HexColor('#f3f4f6')),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.black),
            ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
            ('ALIGN', (2, 0), (2, -1), 'RIGHT'),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, -1), 12),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 12),
            ('GRID', (0, 0), (-1, -1), 1, colors.black),
        ]))
        story.append(payment_table)
        story.append(Spacer(1, 20))

        # Total
        total_data = [
            ['', 'Total Amount:', f'₦{payment.amount:,.2f}']
        ]
        total_table = Table(total_data, colWidths=[3*inch, 1.5*inch, 1.5*inch])
        total_table.setStyle(TableStyle([
            ('ALIGN', (1, 0), (-1, -1), 'RIGHT'),
            ('FONTNAME', (1, 0), (-1, -1), 'Helvetica-Bold'),
            ('FONTSIZE', (1, 0), (-1, -1), 14),
            ('BOTTOMPADDING', (1, 0), (-1, -1), 12),
        ]))
        story.append(total_table)
        story.append(Spacer(1, 30))

        # Footer
        footer_text = """
        <para align=center>
        <b>Green Peak Shelters</b><br/>
        Thank you for your payment!<br/>
        For inquiries, please contact our office.<br/>
        <i>This is a computer-generated receipt.</i>
        </para>
        """
        story.append(Paragraph(footer_text, styles['Normal']))

        # Build the PDF
        doc.build(story)

        # Get the PDF data
        pdf_data = buffer.getvalue()
        buffer.close()

        # Create response
        response = make_response(pdf_data)
        response.headers['Content-Type'] = 'application/pdf'
        response.headers['Content-Disposition'] = f'attachment; filename=receipt-GP-{payment.id:06d}.pdf'

        return response

    except ImportError:
        # If reportlab is not installed, return an error message
        from flask import flash, redirect, url_for
        flash('PDF generation is not available. Please contact support.', 'error')
        return redirect(url_for('client.payment_receipt', payment_id=payment_id))

@client_bp.route('/submit-payment/<string:item_type>/<int:item_id>', methods=['GET', 'POST'])
@role_required(['client'])
def submit_payment(item_type, item_id):
    from models.property import Property
    from models.service import Service
    from models.payment_submission import PaymentSubmission
    from models.database import db
    from flask import g, request, flash, redirect, url_for
    from werkzeug.utils import secure_filename
    import os
    from datetime import datetime, date

    # Validate item_type
    if item_type not in ['property', 'service']:
        flash('Invalid item type', 'error')
        return redirect(url_for('client.dashboard'))

    # Get the item and verify client has access
    if item_type == 'property':
        from models.client_property import ClientProperty
        client_item = ClientProperty.query.filter_by(
            client_id=g.user.id,
            property_id=item_id
        ).first()
        if not client_item:
            flash('Property not found or access denied', 'error')
            return redirect(url_for('client.property_list'))
        item_details = Property.query.get(item_id)
        item_name = item_details.name if item_details else 'Unknown Property'
    else:
        from models.client_service import ClientService
        client_item = ClientService.query.filter_by(
            client_id=g.user.id,
            service_id=item_id
        ).first()
        if not client_item:
            flash('Service not found or access denied', 'error')
            return redirect(url_for('client.dashboard'))
        item_details = Service.query.get(item_id)
        item_name = item_details.name if item_details else 'Unknown Service'

    if request.method == 'POST':
        # Get form data
        amount = request.form.get('amount')
        payment_date = request.form.get('payment_date')
        payment_method = request.form.get('payment_method')
        reference_number = request.form.get('reference_number')
        description = request.form.get('description')
        proof_file = request.files.get('proof_file')

        # Validate required fields
        if not all([amount, payment_date, payment_method]) or not (proof_file and proof_file.filename):
            flash('Please fill in all required fields and upload proof of payment', 'error')
            return render_template('client/submit_payment.html',
                                 item_type=item_type,
                                 item_id=item_id,
                                 item_name=item_name,
                                 item_details=item_details,
                                 today=date.today().isoformat())

        try:
            amount = float(amount)
            if amount <= 0:
                raise ValueError("Amount must be positive")
        except ValueError:
            flash('Please enter a valid amount', 'error')
            return render_template('client/submit_payment.html',
                                 item_type=item_type,
                                 item_id=item_id,
                                 item_name=item_name,
                                 item_details=item_details,
                                 today=date.today().isoformat())

        # Validate payment date
        try:
            payment_date_obj = datetime.strptime(payment_date, '%Y-%m-%d').date()
            if payment_date_obj > date.today():
                flash('Payment date cannot be in the future', 'error')
                return render_template('client/submit_payment.html',
                                     item_type=item_type,
                                     item_id=item_id,
                                     item_name=item_name,
                                     item_details=item_details,
                                     today=date.today().isoformat())
        except ValueError:
            flash('Please enter a valid payment date', 'error')
            return render_template('client/submit_payment.html',
                                 item_type=item_type,
                                 item_id=item_id,
                                 item_name=item_name,
                                 item_details=item_details,
                                 today=date.today().isoformat())

        # Handle file upload
        if proof_file and proof_file.filename:
            # Check file size (10MB limit)
            if len(proof_file.read()) > 10 * 1024 * 1024:
                flash('File size must be less than 10MB', 'error')
                return render_template('client/submit_payment.html',
                                     item_type=item_type,
                                     item_id=item_id,
                                     item_name=item_name,
                                     item_details=item_details,
                                     today=date.today().isoformat())

            # Reset file pointer
            proof_file.seek(0)

            # Validate file type
            allowed_extensions = {'png', 'jpg', 'jpeg', 'pdf'}
            filename = secure_filename(proof_file.filename)
            if not filename or '.' not in filename or \
               filename.rsplit('.', 1)[1].lower() not in allowed_extensions:
                flash('Please upload a valid image (PNG, JPG) or PDF file', 'error')
                return render_template('client/submit_payment.html',
                                     item_type=item_type,
                                     item_id=item_id,
                                     item_name=item_name,
                                     item_details=item_details,
                                     today=date.today().isoformat())

            # Create uploads directory if it doesn't exist
            upload_dir = os.path.join('uploads', 'payment_proofs')
            os.makedirs(upload_dir, exist_ok=True)

            # Generate unique filename
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            file_extension = filename.rsplit('.', 1)[1].lower()
            unique_filename = f"payment_proof_{g.user.id}_{timestamp}.{file_extension}"
            file_path = os.path.join(upload_dir, unique_filename)

            # Save file
            proof_file.save(file_path)

            # Create payment submission record
            submission = PaymentSubmission(
                client_id=g.user.id,
                item_type=item_type,
                item_id=item_id,
                amount=amount,
                payment_date=payment_date_obj,
                payment_method=payment_method,
                reference_number=reference_number,
                description=description,
                proof_filename=filename,
                proof_filepath=file_path,
                proof_mimetype=proof_file.mimetype,
                status='pending'
            )

            db.session.add(submission)
            db.session.commit()

            # Create notification for staff
            from models.notification import Notification
            Notification.create_payment_submission_notification(submission)

            flash('Payment submission successful! Staff will review your payment and update your account.', 'success')
            return redirect(url_for('client.property_detail', property_id=item_id) if item_type == 'property' else url_for('client.dashboard'))

        else:
            flash('Please upload proof of payment', 'error')

    return render_template('client/submit_payment.html',
                         item_type=item_type,
                         item_id=item_id,
                         item_name=item_name,
                         item_details=item_details,
                         today=date.today().isoformat())

@client_bp.route('/support', methods=['GET'])
@role_required(['client'])
def support():
    # Get current user's support tickets
    from models.support_ticket import SupportTicket
    from flask import g
    
    tickets = SupportTicket.query.filter_by(user_id=g.user.id).all()
    return render_template('client/support.html', tickets=tickets)

@client_bp.route('/support/create', methods=['GET', 'POST'])
@role_required(['client'])
def create_ticket():
    from models.support_ticket import SupportTicket
    from models.ticket_message import TicketMessage
    from models.database import db
    from flask import g
    
    if request.method == 'POST':
        subject = request.form.get('subject')
        message = request.form.get('message')
        
        if not subject or not message:
            flash('Subject and message are required', 'error')
            return render_template('client/create_ticket.html')
        
        new_ticket = SupportTicket(
            user_id=g.user.id,
            subject=subject,
            status='open'
        )
        
        db.session.add(new_ticket)
        db.session.commit()
        
        # Add initial message
        ticket_message = TicketMessage(
            ticket_id=new_ticket.id,
            user_id=g.user.id,
            message=message
        )
        
        db.session.add(ticket_message)
        db.session.commit()
        
        flash('Support ticket created successfully', 'success')
        return redirect(url_for('client.support'))
        
    return render_template('client/create_ticket.html')

@client_bp.route('/support/<int:ticket_id>', methods=['GET', 'POST'])
@role_required(['client'])
def view_ticket(ticket_id):
    from models.support_ticket import SupportTicket
    from models.ticket_message import TicketMessage
    from models.database import db
    from flask import g
    
    ticket = SupportTicket.query.filter_by(id=ticket_id, user_id=g.user.id).first_or_404()
    messages = TicketMessage.query.filter_by(ticket_id=ticket.id).order_by(TicketMessage.created_at).all()
    
    if request.method == 'POST':
        message = request.form.get('message')
        
        if not message:
            flash('Message cannot be empty', 'error')
            return redirect(url_for('client.view_ticket', ticket_id=ticket.id))
        
        new_message = TicketMessage(
            ticket_id=ticket.id,
            user_id=g.user.id,
            message=message
        )
        
        db.session.add(new_message)
        db.session.commit()
        
        flash('Reply added successfully', 'success')
        return redirect(url_for('client.view_ticket', ticket_id=ticket.id))
    
    return render_template('client/view_ticket.html', ticket=ticket, messages=messages)
