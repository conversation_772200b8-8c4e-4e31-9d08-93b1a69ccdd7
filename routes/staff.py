
from flask import Blueprint, render_template, g, request, flash, redirect, url_for, jsonify
from utils.auth import role_required
from models.user import User
from models.property import Property
from models.service import Service
from models.payment import Payment
from models.database import db
from datetime import datetime, date
from models.client_property import ClientProperty
from models.client_service import ClientService

staff_bp = Blueprint('staff', __name__, url_prefix='/staff')

@staff_bp.route('/dashboard')
@role_required(['staff'])
def dashboard():
    # Count clients, properties, services, and payments
    client_count = User.query.filter_by(role='client').count()
    property_count = Property.query.count()
    service_count = Service.query.count()
    total_payments = db.session.query(db.func.sum(Payment.amount)).scalar() or 0
    
    # Calculate total amounts paid and owed across all properties and services
    properties = Property.query.all()
    services = Service.query.all()

    # Calculate totals for properties
    total_paid_properties = sum(prop.get_total_paid() for prop in properties)
    total_owed_properties = sum(prop.get_corrected_amount_due() for prop in properties)

    # Calculate totals for services
    total_paid_services = sum(service.get_total_paid() for service in services)
    total_owed_services = sum(service.get_amount_due() for service in services)

    # Grand totals
    total_amount_paid = total_paid_properties + total_paid_services
    total_amount_owed = total_owed_properties + total_owed_services

    # Get properties with overdue payments (for the overdue properties list)
    overdue_properties = [p for p in properties if p.get_corrected_amount_due() > 0]
    
    # Sort overdue properties by amount owed (descending)
    overdue_properties.sort(key=lambda p: p.get_corrected_amount_due(), reverse=True)
    
    # Get monthly payment data for chart
    current_year = datetime.now().year
    monthly_payments = []
    months = []
    
    for month in range(1, 13):
        month_name = datetime(current_year, month, 1).strftime('%b')
        months.append(month_name)
        
        # Get sum of payments for this month
        start_date = datetime(current_year, month, 1)
        if month == 12:
            end_date = datetime(current_year + 1, 1, 1)
        else:
            end_date = datetime(current_year, month + 1, 1)
        
        month_sum = db.session.query(db.func.sum(Payment.amount)).filter(
            Payment.payment_date >= start_date,
            Payment.payment_date < end_date
        ).scalar() or 0
        
        monthly_payments.append(float(month_sum))
    
    # Get recent activities (e.g., recent payments)
    recent_activities = []
    recent_payments = Payment.query.order_by(Payment.payment_date.desc()).limit(10).all()
    
    for payment in recent_payments:
        client = payment.client
        activity_type = "property" if payment.item_type == "property" else "service"
        item_name = ""
        
        if payment.item_type == "property":
            property_obj = Property.query.get(payment.item_id)
            if property_obj:
                item_name = property_obj.name
        else:
            service_obj = Service.query.get(payment.item_id)
            if service_obj:
                item_name = service_obj.name
        
        activity = {
            'date': payment.payment_date,
            'client': client,
            'description': f"Payment for {activity_type}: {item_name}",
            'status': payment.status
        }
        recent_activities.append(activity)
    
    return render_template('staff/dashboard.html',
                          client_count=client_count,
                          property_count=property_count,
                          service_count=service_count,
                          total_payments="{:,.2f}".format(total_payments),
                          total_amount_paid=total_amount_paid,
                          total_amount_owed=total_amount_owed,
                          total_paid_properties=total_paid_properties,
                          total_owed_properties=total_owed_properties,
                          total_paid_services=total_paid_services,
                          total_owed_services=total_owed_services,
                          months=months,
                          monthly_payments=monthly_payments,
                          recent_activities=recent_activities,
                          overdue_properties=overdue_properties[:5])  # Show top 5 most overdue

@staff_bp.route('/clients')
@role_required(['staff'])
def client_list():
    # Get all clients
    clients = User.query.filter_by(role='client').all()
    return render_template('staff/client_list.html', clients=clients)

@staff_bp.route('/clients/create', methods=['GET', 'POST'])
@role_required(['staff'])
def create_client():
    if request.method == 'POST':
        # Get form data
        firstname = request.form.get('firstname')
        lastname = request.form.get('lastname')
        email = request.form.get('email')
        address = request.form.get('address')
        phone = request.form.get('phone')
        
        # Validate data
        if not firstname or not lastname or not email or not phone:
            flash('First name, last name, email, and phone number are required.', 'error')
            return render_template('staff/create_client.html')
        
        # Check if email already exists
        existing_user = User.query.filter_by(email=email).first()
        if existing_user:
            flash('A user with that email already exists.', 'error')
            return render_template('staff/create_client.html')
        
        # Create new client user
        new_client = User(
            firstname=firstname,
            lastname=lastname,
            email=email,
            address=address,
            phone=phone,
            role='client',
            active=True
        )
        
        # Add to database
        db.session.add(new_client)
        db.session.commit()
        
        flash('Client account created successfully.', 'success')
        return redirect(url_for('staff.client_list'))
    
    return render_template('staff/create_client.html')

@staff_bp.route('/properties')
@role_required(['staff'])
def property_list():
    # Get all properties
    properties = Property.query.all()
    return render_template('staff/property_list.html', properties=properties)

@staff_bp.route('/properties/create', methods=['GET', 'POST'])
@role_required(['staff'])
def create_property():
    if request.method == 'POST':
        name = request.form.get('name')
        property_type = request.form.get('type')
        location = request.form.get('location')
        price = request.form.get('price')
        description = request.form.get('description')
        payment_cadence = request.form.get('payment_cadence', 'one_time')
        
        try:
            # Create new property
            new_property = Property(
                name=name,
                property_type=property_type,
                location=location,
                price=float(price),
                description=description,
                payment_cadence=payment_cadence,
                status='Available'
            )
            
            # Add to database
            db.session.add(new_property)
            db.session.commit()
            
            flash('Property created successfully.', 'success')
            return redirect(url_for('staff.property_list'))
        except Exception as e:
            flash(f'Error creating property: {str(e)}', 'error')
            return render_template('staff/create_property.html')
    
    return render_template('staff/create_property.html')

@staff_bp.route('/services')
@role_required(['staff'])
def service_list():
    # Get all services
    services = Service.query.all()
    return render_template('staff/service_list.html', services=services)

@staff_bp.route('/services/create', methods=['GET', 'POST'])
@role_required(['staff'])
def create_service():
    if request.method == 'POST':
        # Get form data
        name = request.form.get('name')
        service_type = request.form.get('type')
        description = request.form.get('description')
        price = request.form.get('price')
        
        # Validate data
        if not name or not service_type or not price:
            flash('Name, type, and price are required.', 'error')
            return render_template('staff/create_service.html')
        
        try:
            # Create new service
            new_service = Service(
                name=name,
                service_type=service_type,
                description=description,
                price=float(price),
                status='Active'
            )
            
            # Add to database
            db.session.add(new_service)
            db.session.commit()
            
            flash('Service created successfully.', 'success')
            return redirect(url_for('staff.service_list'))
        except Exception as e:
            flash(f'Error creating service: {str(e)}', 'error')
            return render_template('staff/create_service.html')
    
    return render_template('staff/create_service.html')

@staff_bp.route('/payments')
@role_required(['staff'])
def payment_list():
    # Get all payments
    payments = Payment.query.order_by(Payment.payment_date.desc()).all()
    return render_template('staff/payment_list.html', payments=payments)

@staff_bp.route('/payments/create', methods=['GET', 'POST'])
@role_required(['staff'])
def create_payment():
    # Get property_id and amount from query parameters if provided
    property_id = request.args.get('property_id', type=int)
    amount = request.args.get('amount', type=float)
    
    # Get all clients and properties for the form
    clients = User.query.filter_by(role='client').all()
    properties = Property.query.all()
    services = Service.query.all()
    
    # Pre-select property and amount if provided
    selected_property = None
    if property_id:
        selected_property = Property.query.get(property_id)
    
    if request.method == 'POST':
        client_id = request.form.get('client_id', type=int)
        item_type = request.form.get('item_type')
        item_id = request.form.get('item_id', type=int)
        payment_amount = request.form.get('amount', type=float)
        payment_date = datetime.strptime(request.form.get('payment_date'), '%Y-%m-%d').date()
        status = request.form.get('status', 'Completed')
        
        # Create new payment
        payment = Payment(
            client_id=client_id,
            item_type=item_type,
            item_id=item_id,
            amount=payment_amount,
            payment_date=payment_date,
            status=status
        )
        
        # Update status based on calculated value
        payment.update_status()
        
        db.session.add(payment)
        db.session.commit()
        
        flash('Payment recorded successfully!', 'success')
        
        # Redirect to property detail if payment was for a property
        if item_type == 'property':
            return redirect(url_for('staff.property_detail', id=item_id))
        else:
            return redirect(url_for('staff.payment_list'))
    
    return render_template('staff/create_payment.html', 
                          clients=clients,
                          properties=properties,
                          services=services,
                          selected_property=selected_property,
                          amount=amount,
                          today=date.today().strftime('%Y-%m-%d'))

@staff_bp.route('/api/items')
@role_required(['staff'])
def get_items():
    item_type = request.args.get('type')
    print(f"API request for items of type: {item_type}")
    
    if item_type == 'property':
        # Get all properties instead of just available ones
        items = Property.query.all()
        print(f"Found {len(items)} properties")
        return jsonify([{'id': p.id, 'name': p.name} for p in items])
    elif item_type == 'service':
        # Get all services instead of just active ones
        items = Service.query.all()
        print(f"Found {len(items)} services")
        return jsonify([{'id': s.id, 'name': s.name} for s in items])
    else:
        print("Invalid item type")
        return jsonify([])

@staff_bp.route('/api/clients')
@role_required(['staff'])
def get_clients():
    clients = User.query.filter_by(role='client').all()
    return jsonify([{
        'id': c.id,
        'name': c.full_name,
        'email': c.email
    } for c in clients])

@staff_bp.route('/api/client/<int:client_id>')
@role_required(['staff'])
def get_client(client_id):
    client = User.query.get_or_404(client_id)
    return jsonify({
        'id': client.id,
        'firstname': client.firstname,
        'lastname': client.lastname,
        'email': client.email,
        'phone': client.phone,
        'address': client.address,
        'active': client.active
    })

@staff_bp.route('/clients/<int:client_id>/assign-property', methods=['GET', 'POST'])
@role_required(['staff'])
def assign_property(client_id):
    client = User.query.get_or_404(client_id)
    
    if request.method == 'POST':
        property_id = request.form.get('property_id')
        
        if not property_id:
            flash('Please select a property.', 'error')
            properties = Property.query.filter_by(status='Available').all()
            return render_template('staff/assign_property.html', client=client, properties=properties)
        
        # Check if already assigned
        existing = ClientProperty.query.filter_by(client_id=client_id, property_id=property_id).first()
        if existing:
            flash('This property is already assigned to this client.', 'error')
            properties = Property.query.filter_by(status='Available').all()
            return render_template('staff/assign_property.html', client=client, properties=properties)
        
        # Create new assignment
        assignment = ClientProperty(client_id=client_id, property_id=property_id)
        db.session.add(assignment)
        
        # Update property status
        property = Property.query.get(property_id)
        property.status = 'Assigned'
        
        db.session.commit()
        flash('Property assigned successfully.', 'success')
        return redirect(url_for('staff.client_list'))
    
    properties = Property.query.filter_by(status='Available').all()
    return render_template('staff/assign_property.html', client=client, properties=properties)

@staff_bp.route('/clients/<int:client_id>/assign-service', methods=['GET', 'POST'])
@role_required(['staff'])
def assign_service(client_id):
    client = User.query.get_or_404(client_id)
    
    if request.method == 'POST':
        service_id = request.form.get('service_id')
        
        if not service_id:
            flash('Please select a service.', 'error')
            services = Service.query.filter_by(status='Active').all()
            return render_template('staff/assign_service.html', client=client, services=services)
        
        # Check if already assigned
        existing = ClientService.query.filter_by(client_id=client_id, service_id=service_id).first()
        if existing:
            flash('This service is already assigned to this client.', 'error')
            services = Service.query.filter_by(status='Active').all()
            return render_template('staff/assign_service.html', client=client, services=services)
        
        # Create new assignment
        assignment = ClientService(client_id=client_id, service_id=service_id)
        db.session.add(assignment)
        db.session.commit()
        
        flash('Service assigned successfully.', 'success')
        return redirect(url_for('staff.client_list'))
    
    services = Service.query.filter_by(status='Active').all()
    return render_template('staff/assign_service.html', client=client, services=services)

@staff_bp.route('/clients/<int:client_id>')
@role_required(['staff'])
def client_detail(client_id):
    # Get client
    client = User.query.get_or_404(client_id)
    
    # Get client's properties
    client_properties = ClientProperty.query.filter_by(client_id=client_id).all()
    properties = [cp.property for cp in client_properties]
    
    # Get client's services
    client_services = ClientService.query.filter_by(client_id=client_id).all()
    services = [cs.service for cs in client_services]
    
    # Get client's payments
    payments = Payment.query.filter_by(client_id=client_id).order_by(Payment.payment_date.desc()).all()
    
    return render_template('staff/client_detail.html', 
                          client=client, 
                          properties=properties, 
                          services=services, 
                          payments=payments)

@staff_bp.route('/clients/<int:client_id>/edit', methods=['GET', 'POST'])
@role_required(['staff'])
def edit_client(client_id):
    # Get client
    client = User.query.get_or_404(client_id)
    
    if request.method == 'POST':
        # Update client data
        client.firstname = request.form.get('firstname')
        client.lastname = request.form.get('lastname')
        client.email = request.form.get('email')
        client.phone = request.form.get('phone')
        client.address = request.form.get('address')
        client.active = 'active' in request.form
        
        # Save changes
        db.session.commit()
        
        flash('Client updated successfully.', 'success')
        return redirect(url_for('staff.client_list'))
    
    return render_template('staff/edit_client.html', client=client)

@staff_bp.route('/reports')
@role_required(['staff'])
def report_list():
    # This is a placeholder for the reports functionality
    # You can implement the actual reports logic based on your requirements
    return render_template('staff/report_list.html')

@staff_bp.route('/properties/<int:id>')
@role_required(['staff'])
def property_detail(id):
    # Get the property
    property = Property.query.get_or_404(id)
    
    # Get clients who have this property
    client_properties = ClientProperty.query.filter_by(property_id=id).all()
    clients = [cp.client for cp in client_properties]
    
    # Get payments for this property
    payments = Payment.query.filter_by(item_type='property', item_id=id).order_by(Payment.payment_date.desc()).all()
    
    return render_template('staff/property_detail.html', 
                          property=property,
                          clients=clients,
                          payments=payments)

@staff_bp.route('/properties/<int:id>/edit', methods=['GET', 'POST'])
@role_required(['staff'])
def edit_property(id):
    # Get the property
    property = Property.query.get_or_404(id)
    
    if request.method == 'POST':
        # Update property data
        property.name = request.form.get('name')
        property.property_type = request.form.get('type')
        property.location = request.form.get('location')
        property.price = float(request.form.get('price'))
        property.description = request.form.get('description')
        property.payment_cadence = request.form.get('payment_cadence', 'one_time')
        property.status = request.form.get('status')
        
        # Save changes
        db.session.commit()
        
        flash('Property updated successfully.', 'success')
        return redirect(url_for('staff.property_detail', id=property.id))
    
    return render_template('staff/edit_property.html', property=property)
