from flask import Blueprint, render_template, request, redirect, url_for, flash, session, g
from models.user import User
from models.database import db
from utils.auth import login_required, create_user_session, invalidate_user_sessions

auth_bp = Blueprint('auth', __name__)

@auth_bp.before_request
def load_logged_in_user():
    user_id = session.get('user_id')
    if user_id is None:
        g.user = None
    else:
        g.user = User.query.get(user_id)

@auth_bp.route('/login', methods=['GET', 'POST'])
def login():
    if request.method == 'POST':
        email = request.form.get('email')
        password = request.form.get('password')
        
        print("\n=== LOGIN ATTEMPT ===")
        print(f"Email: {email}")
        print(f"Password provided: {'Yes' if password else 'No'}")
        
        # Check if the form data is being received correctly
        print("Form data:", request.form)
        
        try:
            # Try to find the user
            user = User.query.filter_by(email=email).first()
            
            if not user:
                print(f"No user found with email: {email}")
                
                # Check if there are any users in the database
                all_users = User.query.all()
                print(f"Total users in database: {len(all_users)}")
                
                if all_users:
                    print("Sample users:")
                    for u in all_users[:5]:
                        print(f"  {u.email} (role: {u.role})")
                
                flash('No account found with that email.', 'error')
                return render_template('auth/login.html', email=email)
            
            print(f"User found: {user.email}")
            print(f"User ID: {user.id}")
            print(f"Role: {user.role}")
            print(f"Active: {user.active}")
            print(f"Has password: {user.has_password()}")
            
            # Check if password is set
            if not user.has_password():
                print("User has no password set, redirecting to set password")
                session['temp_user_id'] = user.id
                return redirect(url_for('auth.set_password'))
            
            # Verify password
            password_correct = user.check_password(password)
            print(f"Password verification result: {password_correct}")
            
            if not password_correct:
                flash('Incorrect password.', 'error')
                return render_template('auth/login.html', email=email)
            
            if not user.is_active:
                flash('This account has been deactivated.', 'error')
                return render_template('auth/login.html', email=email)
            
            # Login successful
            print("Login successful, setting session")

            # Check for remember me option
            remember_me = request.form.get('remember') == 'on'

            # Get client info for session tracking
            ip_address = request.environ.get('HTTP_X_FORWARDED_FOR', request.remote_addr)
            user_agent = request.headers.get('User-Agent', '')

            # Create new session
            create_user_session(
                user=user,
                remember_me=remember_me,
                ip_address=ip_address,
                user_agent=user_agent
            )
            
            # Redirect based on role
            next_page = request.args.get('next')
            if next_page:
                print(f"Redirecting to next page: {next_page}")
                return redirect(next_page)
            
            if user.is_super_user:
                print("Redirecting to admin dashboard")
                return redirect(url_for('admin.dashboard'))
            elif user.is_staff:
                print("Redirecting to staff dashboard")
                return redirect(url_for('staff.dashboard'))
            else:  # client
                print("Redirecting to client dashboard")
                return redirect(url_for('client.dashboard'))
        
        except Exception as e:
            print(f"Error during login: {e}")
            import traceback
            traceback.print_exc()
            flash('An error occurred during login. Please try again.', 'error')
            return render_template('auth/login.html', email=email)
    
    return render_template('auth/login.html')

@auth_bp.route('/set-password', methods=['GET', 'POST'])
def set_password():
    # Check if user is in temporary session
    user_id = session.get('temp_user_id')
    if not user_id:
        return redirect(url_for('auth.login'))
    
    user = User.query.get(user_id)
    if not user:
        session.pop('temp_user_id', None)
        return redirect(url_for('auth.login'))
    
    if request.method == 'POST':
        password = request.form.get('password')
        confirm_password = request.form.get('confirm_password')
        
        if not password or len(password) < 8:
            flash('Password must be at least 8 characters long.', 'error')
            return render_template('auth/set_password.html')
        
        if password != confirm_password:
            flash('Passwords do not match.', 'error')
            return render_template('auth/set_password.html')
        
        # Set the password
        user.set_password(password)
        db.session.commit()
        
        # Clear temporary session and redirect to login
        session.pop('temp_user_id', None)
        flash('Password set successfully. Please login with your new password.', 'success')
        return redirect(url_for('auth.login'))
    
    return render_template('auth/set_password.html')

@auth_bp.route('/logout')
def logout():
    # Invalidate current session in database
    if hasattr(session, 'session_id') and session.session_id:
        from models.session import Session
        db_session = Session.query.filter_by(id=session.session_id).first()
        if db_session:
            db_session.invalidate()
            db.session.commit()

    session.clear()
    return redirect(url_for('index'))

@auth_bp.route('/logout-all')
@login_required
def logout_all():
    """Logout from all devices"""
    if g.user:
        invalidate_user_sessions(g.user.id, except_current=False)
        flash('You have been logged out from all devices.', 'success')

    session.clear()
    return redirect(url_for('index'))

@auth_bp.route('/profile')
@login_required
def profile():
    # Get the current user from the session
    user = g.user
    return render_template('auth/profile.html', user=user)  # Removed the return type

@auth_bp.route('/change-password', methods=['GET', 'POST'])
@login_required
def change_password():
    if request.method == 'POST':
        current_password = request.form.get('current_password')
        new_password = request.form.get('new_password')
        confirm_password = request.form.get('confirm_password')
        
        # Validate current password
        if not g.user.check_password(current_password):
            flash('Current password is incorrect.', 'error')
            return render_template('auth/change_password.html')
        
        # Validate new password
        if new_password != confirm_password:
            flash('New passwords do not match.', 'error')
            return render_template('auth/change_password.html')
        
        # Update password
        g.user.set_password(new_password)
        db.session.commit()
        
        flash('Your password has been updated successfully.', 'success')
        return redirect(url_for('auth.profile'))
    
    return render_template('auth/change_password.html')

@auth_bp.route('/forgot-password', methods=['GET', 'POST'])
def forgot_password():
    if request.method == 'POST':
        email = request.form.get('email')
        
        if not email:
            flash('Email is required.', 'error')
            return render_template('auth/forgot_password.html')
        
        user = User.query.filter_by(email=email).first()
        
        if not user:
            flash('No account found with that email.', 'error')
            return render_template('auth/forgot_password.html')
        
        # In a real application, you would:
        # 1. Generate a reset token
        # 2. Store it in the database with an expiration
        # 3. Send an email with a reset link
        
        # For now, we'll just show a success message
        flash('If an account exists with that email, you will receive password reset instructions.', 'success')
        return redirect(url_for('auth.login'))

    return render_template('auth/forgot_password.html')

@auth_bp.route('/sessions')
@login_required
def sessions():
    """View active sessions"""
    from utils.auth import get_user_sessions

    user_sessions = get_user_sessions(g.user.id, active_only=True)
    current_session_id = getattr(session, 'session_id', None)

    return render_template('auth/sessions.html',
                         sessions=user_sessions,
                         current_session_id=current_session_id)

@auth_bp.route('/sessions/<session_id>/revoke', methods=['POST'])
@login_required
def revoke_session(session_id):
    """Revoke a specific session"""
    from models.session import Session

    # Only allow users to revoke their own sessions
    db_session = Session.query.filter_by(
        id=session_id,
        user_id=g.user.id
    ).first()

    if not db_session:
        flash('Session not found.', 'error')
        return redirect(url_for('auth.sessions'))

    # Don't allow revoking current session
    current_session_id = getattr(session, 'session_id', None)
    if session_id == current_session_id:
        flash('Cannot revoke current session. Use logout instead.', 'error')
        return redirect(url_for('auth.sessions'))

    db_session.invalidate()
    db.session.commit()

    flash('Session revoked successfully.', 'success')
    return redirect(url_for('auth.sessions'))
