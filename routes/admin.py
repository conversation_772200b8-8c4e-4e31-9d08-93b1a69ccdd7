
from flask import Blueprint, render_template, g, request, flash, redirect, url_for
from utils.auth import role_required
from models.user import User
from models.database import db

admin_bp = Blueprint('admin', __name__, url_prefix='/admin')

@admin_bp.route('/dashboard')
@role_required(['super_user'])
def dashboard():
    return render_template('admin/dashboard.html')

@admin_bp.route('/staff')
@role_required(['super_user'])
def staff_list():
    staff = User.query.filter_by(role='staff').all()
    return render_template('admin/staff_list.html', staff=staff)

@admin_bp.route('/staff/create', methods=['GET', 'POST'])
@role_required(['super_user'])
def create_staff():
    if request.method == 'POST':
        # Get form data
        firstname = request.form.get('firstname')
        lastname = request.form.get('lastname')
        email = request.form.get('email')
        address = request.form.get('address')
        phone = request.form.get('phone')  # Added phone field
        designation = request.form.get('designation')
        department = request.form.get('department')
        
        # Validate data
        if not firstname or not lastname or not email:
            flash('First name, last name, and email are required.', 'error')
            return render_template('admin/create_staff.html')
        
        # Check if email already exists
        existing_user = User.query.filter_by(email=email).first()
        if existing_user:
            flash('A user with that email already exists.', 'error')
            return render_template('admin/create_staff.html')
        
        # Create new staff user
        new_staff = User(
            firstname=firstname,
            lastname=lastname,
            email=email,
            address=address,
            phone=phone,  # Added phone field
            designation=designation,
            department=department,
            role='staff',
            active=True
        )
        
        # Add to database
        db.session.add(new_staff)
        db.session.commit()
        
        flash('Staff account created successfully. Staff member will set password on first login.', 'success')
        return redirect(url_for('admin.staff_list'))
    
    return render_template('admin/create_staff.html')

@admin_bp.route('/clients')
@role_required(['super_user'])
def client_list():
    clients = User.query.filter_by(role='client').all()
    return render_template('admin/client_list.html', clients=clients)

@admin_bp.route('/staff/<int:staff_id>')
@role_required(['super_user'])
def staff_detail(staff_id):
    staff = User.query.get_or_404(staff_id)
    return render_template('admin/staff_detail.html', staff=staff)

@admin_bp.route('/clients/<int:client_id>')
@role_required(['super_user'])
def client_detail(client_id):
    client = User.query.filter_by(id=client_id, role='client').first_or_404()
    
    # Get client's properties
    from models.client_property import ClientProperty
    client_properties = ClientProperty.query.filter_by(client_id=client_id).all()
    properties = [cp.property for cp in client_properties]
    
    # Get client's services
    from models.client_service import ClientService
    client_services = ClientService.query.filter_by(client_id=client_id).all()
    services = [cs.service for cs in client_services]
    
    # Get client's payments
    from models.payment import Payment
    payments = Payment.query.filter_by(client_id=client_id).order_by(Payment.payment_date.desc()).limit(5).all()
    
    return render_template('admin/client_detail.html', 
                          client=client, 
                          properties=properties,
                          services=services,
                          payments=payments)

@admin_bp.route('/clients/<int:client_id>/edit', methods=['GET', 'POST'])
@role_required(['super_user'])
def edit_client(client_id):
    # Get client
    client = User.query.get_or_404(client_id)
    
    if request.method == 'POST':
        # Update client data
        client.firstname = request.form.get('firstname')
        client.lastname = request.form.get('lastname')
        client.email = request.form.get('email')
        client.phone = request.form.get('phone')
        client.address = request.form.get('address')
        client.active = 'active' in request.form
        
        # Save changes
        db.session.commit()
        
        flash('Client updated successfully.', 'success')
        return redirect(url_for('admin.client_list'))
    
    return render_template('admin/edit_client.html', client=client)

@admin_bp.route('/settings')
@role_required(['super_user'])
def settings():
    return render_template('admin/settings.html')

@admin_bp.route('/staff/<int:staff_id>/edit', methods=['GET', 'POST'])
@role_required(['super_user'])
def edit_staff(staff_id):
    # Get staff member
    staff = User.query.get_or_404(staff_id)
    
    if request.method == 'POST':
        # Get form data
        firstname = request.form.get('firstname')
        lastname = request.form.get('lastname')
        email = request.form.get('email')
        address = request.form.get('address')
        phone = request.form.get('phone')
        designation = request.form.get('designation')
        department = request.form.get('department')
        active = 'active' in request.form
        
        # Validate data
        if not firstname or not lastname or not email:
            flash('First name, last name, and email are required.', 'error')
            return render_template('admin/edit_staff.html', staff=staff)
        
        # Check if email already exists and belongs to different user
        existing_user = User.query.filter_by(email=email).first()
        if existing_user and existing_user.id != staff_id:
            flash('A user with that email already exists.', 'error')
            return render_template('admin/edit_staff.html', staff=staff)
        
        # Update staff user
        staff.firstname = firstname
        staff.lastname = lastname
        staff.email = email
        staff.address = address
        staff.phone = phone
        staff.designation = designation
        staff.department = department
        staff.active = active
        
        # Save to database
        db.session.commit()
        
        flash('Staff account updated successfully.', 'success')
        return redirect(url_for('admin.staff_list'))
    
    return render_template('admin/edit_staff.html', staff=staff)

@admin_bp.route('/test')
@role_required(['super_user'])
def test():
    return "This is a test route"
