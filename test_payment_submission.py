#!/usr/bin/env python3
"""
Test script to verify payment submission form validation logic
"""

from werkzeug.datastructures import FileStorage
import io

def test_file_validation():
    """Test the file validation logic that was causing issues"""
    
    print("=== Testing File Validation Logic ===")
    
    # Test 1: No file uploaded (empty FileStorage)
    print("\nTest 1: No file uploaded")
    empty_file = FileStorage()
    print(f"Empty FileStorage object: {empty_file}")
    print(f"Empty FileStorage filename: {empty_file.filename}")
    print(f"bool(empty_file): {bool(empty_file)}")
    print(f"empty_file.filename: {empty_file.filename}")
    print(f"bool(empty_file.filename): {bool(empty_file.filename)}")
    print(f"empty_file and empty_file.filename: {empty_file and empty_file.filename}")
    
    # Test 2: File uploaded with content
    print("\nTest 2: File uploaded with content")
    file_content = b"This is a test file content"
    real_file = FileStorage(
        stream=io.BytesIO(file_content),
        filename="test_receipt.pdf",
        content_type="application/pdf"
    )
    print(f"Real FileStorage object: {real_file}")
    print(f"Real FileStorage filename: {real_file.filename}")
    print(f"bool(real_file): {bool(real_file)}")
    print(f"real_file.filename: {real_file.filename}")
    print(f"bool(real_file.filename): {bool(real_file.filename)}")
    print(f"real_file and real_file.filename: {real_file and real_file.filename}")
    
    # Test 3: FileStorage with empty filename
    print("\nTest 3: FileStorage with empty filename")
    empty_filename_file = FileStorage(
        stream=io.BytesIO(b"content"),
        filename="",
        content_type="application/pdf"
    )
    print(f"Empty filename FileStorage object: {empty_filename_file}")
    print(f"Empty filename FileStorage filename: {empty_filename_file.filename}")
    print(f"bool(empty_filename_file): {bool(empty_filename_file)}")
    print(f"empty_filename_file.filename: {empty_filename_file.filename}")
    print(f"bool(empty_filename_file.filename): {bool(empty_filename_file.filename)}")
    print(f"empty_filename_file and empty_filename_file.filename: {empty_filename_file and empty_filename_file.filename}")
    
    # Test validation logic
    print("\n=== Testing Validation Logic ===")
    
    def validate_file(proof_file):
        """Test the validation logic"""
        return proof_file and proof_file.filename
    
    print(f"Empty file validation: {validate_file(empty_file)}")
    print(f"Real file validation: {validate_file(real_file)}")
    print(f"Empty filename file validation: {validate_file(empty_filename_file)}")
    
    # Test form data simulation
    print("\n=== Simulating Form Data ===")
    
    def simulate_form_validation(amount, payment_date, payment_method, proof_file):
        """Simulate the form validation logic"""
        required_fields_valid = all([amount, payment_date, payment_method])
        file_valid = proof_file and proof_file.filename
        
        print(f"Amount: {amount}")
        print(f"Payment Date: {payment_date}")
        print(f"Payment Method: {payment_method}")
        print(f"Proof File: {proof_file}")
        print(f"Required fields valid: {required_fields_valid}")
        print(f"File valid: {file_valid}")
        print(f"Overall validation: {required_fields_valid and file_valid}")
        
        return required_fields_valid and file_valid
    
    # Test case 1: All fields filled, no file
    print("\nTest Case 1: All fields filled, no file")
    result1 = simulate_form_validation("1000", "2025-06-22", "bank_transfer", empty_file)
    print(f"Result: {result1}")
    
    # Test case 2: All fields filled, with file
    print("\nTest Case 2: All fields filled, with file")
    result2 = simulate_form_validation("1000", "2025-06-22", "bank_transfer", real_file)
    print(f"Result: {result2}")
    
    # Test case 3: Missing amount, with file
    print("\nTest Case 3: Missing amount, with file")
    result3 = simulate_form_validation("", "2025-06-22", "bank_transfer", real_file)
    print(f"Result: {result3}")

if __name__ == "__main__":
    test_file_validation()
