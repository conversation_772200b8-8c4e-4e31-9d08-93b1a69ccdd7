#!/usr/bin/env python3
"""
Test script to verify the client dashboard calculations
"""

from flask import Flask
from models.database import db
from models.user import User
from models.property import Property
from models.service import Service
from models.payment import Payment
from models.client_property import ClientProperty
from models.client_service import ClientService

def create_app():
    """Create Flask app with database configuration"""
    app = Flask(__name__)
    app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///greenpeak.db'
    app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
    db.init_app(app)
    return app

def test_client_dashboard():
    """Test the client dashboard calculations for Emma"""
    app = create_app()
    
    with app.app_context():
        print("=== Testing Client Dashboard Calculations ===\n")
        
        # Find Emma
        emma = User.query.filter_by(email='<EMAIL>').first()
        if not emma:
            print("Emma not found in database!")
            return
        
        print(f"Testing dashboard for: {emma.full_name} ({emma.email})")
        print(f"Client ID: {emma.id}\n")
        
        # Count properties and services
        property_count = ClientProperty.query.filter_by(client_id=emma.id).count()
        service_count = ClientService.query.filter_by(client_id=emma.id).count()
        
        # Sum total payments made by this client
        total_payments = db.session.query(db.func.sum(Payment.amount)).filter_by(client_id=emma.id).scalar() or 0
        
        print(f"Basic Counts:")
        print(f"  Properties: {property_count}")
        print(f"  Services: {service_count}")
        print(f"  Total Payments: ${total_payments:,.2f}\n")
        
        # Calculate amounts paid and owed for properties
        client_properties = ClientProperty.query.filter_by(client_id=emma.id).all()
        total_paid_properties = 0
        total_owed_properties = 0
        
        print("--- Property Analysis ---")
        for cp in client_properties:
            property_obj = Property.query.get(cp.property_id)
            if property_obj:
                paid = property_obj.get_total_paid()
                owed = property_obj.get_corrected_amount_due()
                total_paid_properties += paid
                total_owed_properties += owed
                
                print(f"Property: {property_obj.name}")
                print(f"  Paid: ${paid:,.2f}")
                print(f"  Owed: ${owed:,.2f}")
        
        print(f"\nProperty Totals:")
        print(f"  Total Paid: ${total_paid_properties:,.2f}")
        print(f"  Total Owed: ${total_owed_properties:,.2f}")
        
        # Calculate amounts paid and owed for services
        client_services = ClientService.query.filter_by(client_id=emma.id).all()
        total_paid_services = 0
        total_owed_services = 0
        
        print("\n--- Service Analysis ---")
        for cs in client_services:
            service_obj = Service.query.get(cs.service_id)
            if service_obj:
                paid = service_obj.get_total_paid()
                owed = service_obj.get_amount_due()
                total_paid_services += paid
                total_owed_services += owed
                
                print(f"Service: {service_obj.name}")
                print(f"  Paid: ${paid:,.2f}")
                print(f"  Owed: ${owed:,.2f}")
        
        print(f"\nService Totals:")
        print(f"  Total Paid: ${total_paid_services:,.2f}")
        print(f"  Total Owed: ${total_owed_services:,.2f}")
        
        # Calculate grand totals
        total_amount_paid = total_paid_properties + total_paid_services
        total_amount_owed = total_owed_properties + total_owed_services
        
        print(f"\n=== DASHBOARD SUMMARY ===")
        print(f"Total Amount Paid: ${total_amount_paid:,.2f}")
        print(f"Total Amount Owed: ${total_amount_owed:,.2f}")
        print(f"Expected Emma's Debt: $2,730,000.00")
        print(f"Match: {'✅ YES' if abs(total_amount_owed - 2730000) < 1 else '❌ NO'}")
        
        # Verify the total payments calculation
        print(f"\nPayment Verification:")
        print(f"  Sum of all payments by Emma: ${total_payments:,.2f}")
        print(f"  Sum of paid amounts (properties + services): ${total_amount_paid:,.2f}")
        print(f"  Match: {'✅ YES' if abs(total_payments - total_amount_paid) < 1 else '❌ NO'}")

if __name__ == "__main__":
    test_client_dashboard()
