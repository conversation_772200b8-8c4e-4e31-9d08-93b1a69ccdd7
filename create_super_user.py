from app import app
from models.database import db
from models.user import User

def create_super_user():
    with app.app_context():
        # Check if super user already exists
        super_user = User.query.filter_by(email='<EMAIL>').first()
        
        if super_user:
            print("Super user already exists.")
            return
        
        # Create super user
        super_user = User(
            email='<EMAIL>',
            firstname='Admin',
            lastname='User',
            role='super_user',
            active=True
        )
        
        # Set password
        super_user.set_password('Gr33nP3@k5hel+3rs2025')
        
        # Add to database
        db.session.add(super_user)
        db.session.commit()
        
        print("Super user created successfully.")
        
        # List all users
        all_users = User.query.all()
        print(f"Total users in database: {len(all_users)}")
        for user in all_users:
            print(f"User: {user.email}, Role: {user.role}, Has Password: {user.has_password()}")

if __name__ == "__main__":
    create_super_user()